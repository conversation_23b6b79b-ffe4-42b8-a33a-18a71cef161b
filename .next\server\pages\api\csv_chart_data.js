"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/csv_chart_data";
exports.ids = ["pages/api/csv_chart_data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\csv_chart_data.ts */ \"(api)/./src/pages/api/csv_chart_data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/csv_chart_data\",\n        pathname: \"/api/csv_chart_data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNzdl9jaGFydF9kYXRhJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNhcGklNUNjc3ZfY2hhcnRfZGF0YS50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDRTtBQUMxRDtBQUNpRTtBQUNqRTtBQUNBLGlFQUFlLHdFQUFLLENBQUMsNkRBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLDZEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLHlHQUFtQjtBQUNsRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vc3JjXFxcXHBhZ2VzXFxcXGFwaVxcXFxjc3ZfY2hhcnRfZGF0YS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY3N2X2NoYXJ0X2RhdGFcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jc3ZfY2hhcnRfZGF0YVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/csv_chart_data.ts":
/*!*****************************************!*\
  !*** ./src/pages/api/csv_chart_data.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// src/pages/api/csv_chart_data.ts\n\n\nasync function handler(req, res) {\n    const { symbol } = req.query;\n    if (!symbol || Array.isArray(symbol)) {\n        return res.status(400).json({\n            error: 'symbol 파라미터가 필요합니다.'\n        });\n    }\n    try {\n        // CSV 파일 경로\n        const csvPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'sp500_close_3y.csv');\n        // CSV 파일 읽기\n        const csvContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(csvPath, 'utf-8');\n        const lines = csvContent.split('\\n');\n        if (lines.length < 2) {\n            return res.status(500).json({\n                error: 'CSV 파일이 비어있습니다.'\n            });\n        }\n        // 헤더 파싱 (첫 번째 줄)\n        const headers = lines[0].split(',');\n        const symbolIndex = headers.findIndex((header)=>header.trim() === symbol.toUpperCase());\n        if (symbolIndex === -1) {\n            return res.status(404).json({\n                error: `심볼 ${symbol}을 찾을 수 없습니다.`\n            });\n        }\n        // 데이터 파싱\n        const chartData = [];\n        for(let i = 1; i < lines.length; i++){\n            const line = lines[i].trim();\n            if (!line) continue;\n            const values = line.split(',');\n            const date = values[0];\n            const priceStr = values[symbolIndex];\n            if (date && priceStr && priceStr !== '') {\n                const price = parseFloat(priceStr);\n                if (!isNaN(price) && price > 0) {\n                    chartData.push({\n                        time: date,\n                        value: price\n                    });\n                }\n            }\n        }\n        if (chartData.length === 0) {\n            return res.status(404).json({\n                error: `${symbol}에 대한 유효한 데이터가 없습니다.`\n            });\n        }\n        // 날짜순 정렬 (오래된 것부터)\n        chartData.sort((a, b)=>new Date(a.time).getTime() - new Date(b.time).getTime());\n        return res.status(200).json({\n            data: chartData,\n            symbol: symbol.toUpperCase()\n        });\n    } catch (error) {\n        console.error('CSV chart data error:', error);\n        return res.status(500).json({\n            error: '차트 데이터를 불러오는 중 오류가 발생했습니다.'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/csv_chart_data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();