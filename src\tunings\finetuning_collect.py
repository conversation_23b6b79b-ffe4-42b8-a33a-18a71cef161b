"""
7 개 분석 스크립트를 순차 실행해 파인튜닝용 JSON 저장
──────────────────────────────────────────────────────────
· 저장:  src/data/finetuning/<UTC_타임스탬프>.json
· 인자 없으면 50 개 기본 티커 사용
· JSON에는 티커/기업명 미포함 → 정보 누수 방지
"""

import json, re, subprocess, sys, warnings
from datetime import datetime
from pathlib import Path

# ── 경로 ─────────────────────────────────────────────────────────
ROOT_SRC = Path(__file__).resolve().parents[1]        # …/src
SERV      = ROOT_SRC / "services"                     # …/src/services
DATA_FT   = ROOT_SRC / "data" / "finetuning"
DATA_FT.mkdir(parents=True, exist_ok=True)

# ── 50 개 기본 티커 ──────────────────────────────────────────────
DEFAULT_TICKERS = [
    "AEP","ACGL","ADM","ALGN","AXP","BEN","BR","CBRE","CCI","CE",
    "CEG","CF","CHRW","CI","CME","COST","CPT","CRM","CSCO","DHR",
    "EMN","FANG","GILD","GPN","IFF","INTU","JNJ","KMB","LIN","LRCX",
    "MAA","MHK","MRO","MS","NCLH","NSC","NTRS","NVDA","PFE","PNW",
    "SOLV","SYK","TXN","VRSN","WBA","WM","WRB","XEL"
]

# ── 분석 스크립트 ↔ 주요 필드 ───────────────────────────────────
SCRIPTS = {
    "RSI"     : ("rsi_service.py",        ["rsi_value",      "traffic_light"]),
    "BOLL"    : ("bollinger_service.py",  ["percent_b",      "traffic_light"]),
    "MFI"     : ("mfi_service.py",        ["mfi_value",      "traffic_light"]),
    "CAPM"    : ("capm_service.py",       ["beta_market",    "r2_market",
                                           "tstat_market",   "traffic_light"]),
    "GARCH"   : ("garch_service.py",      ["var95_pct",      "traffic_light"]),
    "INDUSTRY": ("industry_regression_service.py",
                 ["beta_industry", "r2_industry", "tstat_industry", "traffic_light"]),
    "LSTM"    : ("lstm_service.py",       ["pred_prob_up",   "traffic_light"]),
}

# ── 표준 키 & 별칭 ──────────────────────────────────────────────
KEY_MAP = {
    "rsi_value"      : {"rsi_value","rsi value","rsi"},
    "mfi_value"      : {"mfi_value","mfi value","mfi"},
    "percent_b"      : {"percent_b","percent b","percentb","boll_percent_b"},
    "beta_market"    : {"beta_market","beta mkt"},
    "r2_market"      : {"r2_market","r2 mkt"},
    "tstat_market"   : {"tstat_market","t_market"},
    "beta_industry"  : {"beta_industry","beta_ind","beta"},
    "r2_industry"    : {"r2_industry","r2_ind","r2"},
    "tstat_industry" : {"tstat_industry","t_ind","tstat"},
    "sigma_pct"      : {"sigma_pct","sigma"},
    "var95_pct"      : {"var95_pct","var_pct","var95"},
    "pred_prob_up"   : {"pred_prob_up","pred_prob","prob_up"},
    "traffic_light"  : {"traffic_light","traffic light","light"},
}

ALIAS2STD = {alias: std for std, aliases in KEY_MAP.items() for alias in aliases}

def _norm(k: str) -> str:
    """공백→_ / 소문자화 후 별칭 매핑"""
    k = re.sub(r"_+", "_", re.sub(r"\s+", "_", k.strip().lower()))
    return ALIAS2STD.get(k, k)

# ── 하위 프로세스 실행 & 디코딩 안전 ────────────────────────────
def run(script: str, ticker: str) -> dict:
    proc = subprocess.run(
        ["python", str(SERV / script), ticker],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False
    )

    if proc.stderr:
        err = proc.stderr.decode("utf-8", "replace").strip()
        if err:
            print(f"[{script} stderr] {err}", file=sys.stderr)

    if proc.returncode:
        raise RuntimeError(f"{script}: exit {proc.returncode}")

    raw = json.loads(proc.stdout.decode("utf-8", "replace"))
    return {_norm(k): v for k, v in raw.items()}

# ── user 메시지 조립 ───────────────────────────────────────────
def build_user_string(res: dict) -> str:
    segs = []
    for tag, (_, keys_raw) in SCRIPTS.items():
        keys = [_norm(k) for k in keys_raw]
        part_vals = ", ".join(f"{k.replace('_',' ').title()}: {res[tag][k]}"
                              for k in keys[:-1])
        segs.append(f"[{part_vals}, Traffic light: {res[tag]['traffic_light'].upper()}]")
    return " ".join(segs)

# ── 단일 티커 처리 ─────────────────────────────────────────────
def collect_one(tkr: str):
    results = {tag: run(script, tkr) for tag, (script, _) in SCRIPTS.items()}
    user_msg = build_user_string(results)

    convo = {
        "messages": [
            {"role": "system",
             "content": ("당신은 이 데이터를 바탕으로 투자 의견을 제시하는 맞춤 투자지원 AI입니다. "
                         "친근하면서도 전문적인 어조(이모티콘 최대 2개)를 유지하세요.")},
            {"role": "user", "content": user_msg},
            {"role": "assistant", "content": ""}
        ]
    }

    ts = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    (DATA_FT / f"{ts}.json").write_text(
        json.dumps(convo, ensure_ascii=False, indent=2), encoding="utf-8"
    )
    print(f"✔ {tkr} → {ts}.json 저장")

# ── 메인 루프 ──────────────────────────────────────────────────
if __name__ == "__main__":
    warnings.filterwarnings("ignore", category=RuntimeWarning)

    tickers = [t.upper() for t in sys.argv[1:]] or DEFAULT_TICKERS
    for t in tickers:
        try:
            collect_one(t)
        except Exception as e:
            print(f"[ERROR] {t}: {e}", file=sys.stderr)
