import { NextApiRequest, NextApiResponse } from 'next';
import * as fs from 'fs';
import * as path from 'path';

// API endpoint to save fine-tuning data after complete SpeedTraffic analysis
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { symbol, phase1Data, phase2Data } = req.body;

  if (!symbol || typeof symbol !== 'string') {
    return res.status(400).json({ error: 'Symbol is required' });
  }

  if (!phase1Data || !phase2Data) {
    return res.status(400).json({ error: 'Both phase1Data and phase2Data are required' });
  }

  const ticker = symbol.toUpperCase();

  try {
    // Create finetuning directory if it doesn't exist
    const finetuningDir = path.join(process.cwd(), 'src', 'data', 'finetuning');
    if (!fs.existsSync(finetuningDir)) {
      fs.mkdirSync(finetuningDir, { recursive: true });
    }

    // Generate timestamp in YYYYMMDD_HHMMSS format
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;
    
    const filename = `${ticker}_${timestamp}.json`;
    const filepath = path.join(finetuningDir, filename);

    // Extract all service metrics for the 7 analysis components
    
    // From Phase 1 data
    const mfiResult = phase1Data.mfi;
    const bollingerResult = phase1Data.bollinger;
    const rsiResult = phase1Data.rsi;
    const industryResult = phase1Data.industry;
    const capmResult = phase1Data.capm;
    const garchResult = phase1Data.garch;

    // From Phase 2 data
    const lstmResult = phase2Data.lstm;

    // Helper functions for value extraction
    const getMFIValue = (result: any): number => result?.mfi_14 || result?.mfi || 0;
    const getBollingerValue = (result: any): number => result?.percent_b || result?.bollinger_position || result?.position || 0;
    const getRSIValue = (result: any): number => result?.rsi_14 || result?.rsi || 0;
    const getCAPMBeta = (result: any): number => result?.beta_market || result?.beta || 0;
    const getCAPMR2 = (result: any): number => result?.r2_market || result?.r2 || 0;
    const getCAPMTStat = (result: any): number => result?.tstat_market || result?.tstat || 0;
    const getGARCHSigma = (result: any): number => result?.sigma_pct || result?.sigma || 0;
    const getGARCHValue = (result: any): number => result?.var95_pct || result?.var_pct || result?.volatility_forecast || result?.volatility || 0;
    const getIndustryName = (result: any): string => result?.industry || 'unknown';
    const getIndustryBeta = (result: any): number => result?.beta || 0;
    const getIndustryR2 = (result: any): number => result?.r2 || 0;
    const getIndustryTStat = (result: any): number => result?.tstat || 0;
    const getServiceTrafficLight = (result: any): string => result?.traffic_light || result?.color || 'red';
    const calculateLSTMAccuracy = (predictions: any[]): number => {
      if (!Array.isArray(predictions) || predictions.length === 0) return 0;
      const correctCount = predictions.filter(p => p.predicted_label === p.actual_label).length;
      return Math.round((correctCount / predictions.length) * 100);
    };
    const getLSTMPredProbUp = (result: any): number => {
      if (result?.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {
        return result.predictions[0].pred_prob_up || 0;
      }
      return result?.pred_prob_up || 0;
    };
    const getLSTMTrafficLight = (result: any): string => {
      if (result?.predictions && Array.isArray(result.predictions)) {
        const correctCount = result.predictions.filter((p: any) => p.predicted_label === p.actual_label).length;
        if (correctCount === 2) return 'green';
        if (correctCount === 1) return 'yellow';
        return 'red';
      }
      return result?.result_color || result?.color || 'red';
    };

    // Extract values for all 7 components
    
    // 1. RSI
    const rsiValue = rsiResult ? getRSIValue(rsiResult) : 0;
    const rsiColor = rsiResult ? getServiceTrafficLight(rsiResult) : 'red';

    // 2. Bollinger Bands
    const bollingerValue = bollingerResult ? getBollingerValue(bollingerResult) : 0;
    const bollingerColor = bollingerResult ? getServiceTrafficLight(bollingerResult) : 'red';

    // 3. MFI
    const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;
    const mfiColor = mfiResult ? getServiceTrafficLight(mfiResult) : 'red';

    // 4. CAPM Market Beta
    const betaMarket = capmResult ? getCAPMBeta(capmResult) : 0;
    const r2Market = capmResult ? getCAPMR2(capmResult) : 0;
    const tstatMarket = capmResult ? getCAPMTStat(capmResult) : 0;
    const capmColor = capmResult ? getServiceTrafficLight(capmResult) : 'red';

    // 5. GARCH Volatility
    const sigmaPct = garchResult ? getGARCHSigma(garchResult) : 0;
    const var95Pct = garchResult ? getGARCHValue(garchResult) : 0;
    const garchColor = garchResult ? getServiceTrafficLight(garchResult) : 'red';

    // 6. Industry Regression
    const industryName = industryResult ? getIndustryName(industryResult) : 'unknown';
    const betaIndustry = industryResult ? getIndustryBeta(industryResult) : 0;
    const r2Industry = industryResult ? getIndustryR2(industryResult) : 0;
    const tstatIndustry = industryResult ? getIndustryTStat(industryResult) : 0;
    const industryColor = industryResult ? getServiceTrafficLight(industryResult) : 'red';

    // 7. LSTM
    const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;
    const lstmPredProbUp = lstmResult ? getLSTMPredProbUp(lstmResult) : 0;
    const lstmColor = lstmResult ? getLSTMTrafficLight(lstmResult) : 'red';

    // Create fine-tuning data in OpenAI format with all 7 components
    const finetuningData = {
      messages: [
        { role: "system", content: "" },
        {
          role: "user",
          content: `[RSI value: ${rsiValue}, Traffic light: ${rsiColor}] [Bollinger %B: ${bollingerValue}, Traffic light: ${bollingerColor}] [MFI value: ${mfiValue}, Traffic light: ${mfiColor}] [Market Beta: ${betaMarket}, R²: ${r2Market}, t-stat: ${tstatMarket}, Traffic light: ${capmColor}] [Volatility: ${sigmaPct}%, VaR 95%: ${var95Pct}%, Traffic light: ${garchColor}] [Industry: ${industryName}, Beta: ${betaIndustry}, R²: ${r2Industry}, t-stat: ${tstatIndustry}, Traffic light: ${industryColor}] [LSTM accuracy: ${lstmAccuracy}%, Prediction probability up: ${lstmPredProbUp}, Traffic light: ${lstmColor}]`
        },
        { role: "assistant", content: "" }
      ]
    };

    fs.writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');
    console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);

    res.status(200).json({ 
      success: true, 
      filename,
      message: 'Fine-tuning data saved successfully' 
    });

  } catch (error) {
    console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({
      error: 'Failed to save fine-tuning data',
      message: errorMessage,
      timestamp: new Date().toISOString()
    });
  }
}
