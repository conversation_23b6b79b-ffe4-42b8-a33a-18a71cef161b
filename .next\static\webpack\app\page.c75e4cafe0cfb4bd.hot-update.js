"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SpeedTraffic.tsx":
/*!*****************************************!*\
  !*** ./src/components/SpeedTraffic.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst SpeedTraffic = (param)=>{\n    let { symbol, onPhaseMessage } = param;\n    _s();\n    // Market indicators state (Pre-ticker mode)\n    const [indicators, setIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Traffic lights state (Post-ticker mode) - New order\n    const [technicalLight, setTechnicalLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 1: Technical Analysis\n    const [industryLight, setIndustryLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 2: Industry Sensitivity\n    const [marketLight, setMarketLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 3: Market Sensitivity (CAPM)\n    const [riskLight, setRiskLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 4: Volatility Risk\n    const [neuralLight, setNeuralLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 5: Neural Network (LSTM)\n    // Prediction state\n    const [phase1Loading, setPhase1Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phase2Loading, setPhase2Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lstmLoading, setLstmLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [predictionError, setPredictionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTimeoutMessage, setShowTimeoutMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastRequestTime, setLastRequestTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Single-flight guard\n    const inFlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 실제 시장 데이터 가져오기 (Pre-ticker mode)\n    const fetchMarketData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/market_data');\n            const result = await response.json();\n            if (result.success && result.data) {\n                const formattedData = result.data.map((item)=>({\n                        label: item.label,\n                        value: item.price.toLocaleString('en-US', {\n                            minimumFractionDigits: 2,\n                            maximumFractionDigits: 2\n                        }),\n                        change: \"\".concat(item.changePercent >= 0 ? '+' : '').concat(item.changePercent.toFixed(2), \"%\"),\n                        trend: item.trend,\n                        color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'\n                    }));\n                setIndicators(formattedData);\n                setLastUpdate(new Date().toLocaleTimeString('ko-KR'));\n            }\n        } catch (error) {\n            console.error('Failed to fetch market data:', error);\n            // 에러 시 fallback 데이터 사용\n            setIndicators([\n                {\n                    label: 'S&P 500',\n                    value: '4,567.89',\n                    change: '+1.2%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '나스닥',\n                    value: '14,234.56',\n                    change: '+0.8%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '다우존스',\n                    value: '34,567.12',\n                    change: '-0.3%',\n                    trend: 'down',\n                    color: 'red'\n                },\n                {\n                    label: 'VIX',\n                    value: '18.45',\n                    change: '-2.1%',\n                    trend: 'down',\n                    color: 'yellow'\n                },\n                {\n                    label: '달러/원',\n                    value: '1,327.50',\n                    change: '+0.5%',\n                    trend: 'up',\n                    color: 'green'\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Convert result color to traffic light status\n    const resultColorToStatus = (color)=>{\n        switch(color === null || color === void 0 ? void 0 : color.toLowerCase()){\n            case 'green':\n            case 'good':\n                return 'good';\n            case 'yellow':\n            case 'warning':\n                return 'warning';\n            case 'red':\n            case 'danger':\n                return 'danger';\n            default:\n                return 'warning';\n        }\n    };\n    // Extract color from LSTM/MFI results\n    const getColorFromResult = (obj)=>{\n        if (!obj) return undefined;\n        if (typeof obj === 'string') return obj;\n        if (obj.result_color) return obj.result_color;\n        if (obj.traffic_light) return obj.traffic_light;\n        if (obj.color) return obj.color;\n        return undefined;\n    };\n    // Staged execution: Phase 1 (Fast services) + Phase 2 (LSTM)\n    const fetchStagedPrediction = async ()=>{\n        if (!symbol || inFlight.current) return;\n        // Prevent too frequent requests (minimum 10 seconds between requests)\n        const now = Date.now();\n        if (now - lastRequestTime < 10000) {\n            console.log('Prediction request throttled - too frequent');\n            return;\n        }\n        try {\n            // Set single-flight guard\n            inFlight.current = true;\n            setLastRequestTime(now);\n            // Reset all lights to inactive state\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n            setPredictionError(null);\n            setShowTimeoutMessage(false);\n            console.log(\"[SpeedTraffic] Starting staged prediction for \".concat(symbol));\n            // Phase 1: Execute fast services (Technical, Industry, Market, Volatility)\n            setPhase1Loading(true);\n            console.log(\"[SpeedTraffic] Phase 1: Starting fast services for \".concat(symbol));\n            const phase1Response = await fetch(\"/api/speedtraffic_staged?symbol=\".concat(symbol, \"&stage=phase1\"), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!phase1Response.ok) {\n                throw new Error(\"Phase 1 HTTP \".concat(phase1Response.status, \": \").concat(phase1Response.statusText));\n            }\n            const phase1Result = await phase1Response.json();\n            console.log(\"[SpeedTraffic] Phase 1 result:\", phase1Result);\n            // Update lights 1-4 immediately after Phase 1 completes\n            if (phase1Result.traffic_lights) {\n                if (phase1Result.traffic_lights.technical) {\n                    setTechnicalLight(resultColorToStatus(phase1Result.traffic_lights.technical));\n                    console.log(\"[SpeedTraffic] Technical light set to: \".concat(phase1Result.traffic_lights.technical));\n                }\n                if (phase1Result.traffic_lights.industry) {\n                    setIndustryLight(resultColorToStatus(phase1Result.traffic_lights.industry));\n                    console.log(\"[SpeedTraffic] Industry light set to: \".concat(phase1Result.traffic_lights.industry));\n                }\n                if (phase1Result.traffic_lights.market) {\n                    setMarketLight(resultColorToStatus(phase1Result.traffic_lights.market));\n                    console.log(\"[SpeedTraffic] Market light set to: \".concat(phase1Result.traffic_lights.market));\n                }\n                if (phase1Result.traffic_lights.risk) {\n                    setRiskLight(resultColorToStatus(phase1Result.traffic_lights.risk));\n                    console.log(\"[SpeedTraffic] Risk light set to: \".concat(phase1Result.traffic_lights.risk));\n                }\n            }\n            setPhase1Loading(false);\n            console.log(\"[SpeedTraffic] Phase 1 completed successfully for \".concat(symbol));\n            // Send Phase 1 completion message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('기술적 분석, 산업 민감도, 시장 민감도, 변동성 리스크 분석을 마쳤어요! 📊');\n            // Phase 2: Execute LSTM service\n            setPhase2Loading(true);\n            setLstmLoading(true);\n            console.log(\"[SpeedTraffic] Phase 2: Starting LSTM service for \".concat(symbol));\n            // Send Phase 2 entry message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('이제 딥러닝 기반 가격 변동 예측을 진행합니다. 잠시만 기다려 주세요! 🤖');\n            // Start 20-second timer for Korean timeout message\n            const timeoutTimer = setTimeout(()=>{\n                setShowTimeoutMessage(true);\n            }, 20000);\n            const phase2Response = await fetch(\"/api/speedtraffic_staged?symbol=\".concat(symbol, \"&stage=phase2\"), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(60000)\n            });\n            clearTimeout(timeoutTimer);\n            if (!phase2Response.ok) {\n                throw new Error(\"Phase 2 HTTP \".concat(phase2Response.status, \": \").concat(phase2Response.statusText));\n            }\n            const phase2Result = await phase2Response.json();\n            console.log(\"[SpeedTraffic] Phase 2 result:\", phase2Result);\n            // Update light 5 after Phase 2 completes\n            if (phase2Result.traffic_lights && phase2Result.traffic_lights.neural) {\n                setNeuralLight(resultColorToStatus(phase2Result.traffic_lights.neural));\n                console.log(\"[SpeedTraffic] Neural light set to: \".concat(phase2Result.traffic_lights.neural));\n            }\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            console.log(\"[SpeedTraffic] Phase 2 completed successfully for \".concat(symbol));\n            // Send Phase 2 completion message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('모든 분석이 완료되었습니다! 결과를 확인해 보세요. ✨');\n            // REMOVED: Automatic fine-tuning data collection\n            // User prefers manual fine-tuning data collection via standalone script\n            console.log(\"[SpeedTraffic] All phases completed successfully for \".concat(symbol));\n        } catch (error) {\n            console.error('Staged prediction error:', error);\n            if (error instanceof Error) {\n                if (error.name === 'TimeoutError') {\n                    setPredictionError('요청 시간 초과');\n                } else {\n                    setPredictionError(\"예측 실패: \".concat(error.message));\n                }\n            } else {\n                setPredictionError('예측 서비스 연결 실패');\n            }\n            // Reset all lights to inactive on error\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n        } finally{\n            setPhase1Loading(false);\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            setShowTimeoutMessage(false);\n            inFlight.current = false;\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (symbol) {\n                // When symbol is provided, fetch staged prediction once\n                fetchStagedPrediction();\n            } else {\n                // When no symbol, fetch market data initially\n                fetchMarketData();\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (!symbol) {\n                const interval = setInterval({\n                    \"SpeedTraffic.useEffect.interval\": ()=>{\n                        fetchMarketData();\n                    }\n                }[\"SpeedTraffic.useEffect.interval\"], 20000);\n                return ({\n                    \"SpeedTraffic.useEffect\": ()=>clearInterval(interval)\n                })[\"SpeedTraffic.useEffect\"];\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // Utility functions for rendering\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case 'up':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M7 17l9.2-9.2M17 17V7H7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, undefined);\n            case 'down':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 7l-9.2 9.2M7 7v10h10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-slate-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 12H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const getChangeColor = (trend)=>{\n        switch(trend){\n            case 'up':\n                return 'text-green-600';\n            case 'down':\n                return 'text-red-600';\n            default:\n                return 'text-slate-500';\n        }\n    };\n    const getTrafficLightColor = (status)=>{\n        switch(status){\n            case 'good':\n                return 'bg-green-500';\n            case 'warning':\n                return 'bg-yellow-500';\n            case 'danger':\n                return 'bg-red-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'good':\n                return '양호';\n            case 'warning':\n                return '보통';\n            case 'danger':\n                return '주의';\n            default:\n                return '분석중';\n        }\n    };\n    // Post-ticker mode: Traffic lights display\n    if (symbol) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-xl border border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(technicalLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        technicalLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(technicalLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"기술적 분석\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(technicalLight === 'inactive' ? 'text-gray-400' : technicalLight === 'good' ? 'text-green-300' : technicalLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(industryLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(industryLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(industryLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        industryLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(industryLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(industryLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"산업 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(industryLight === 'inactive' ? 'text-gray-400' : industryLight === 'good' ? 'text-green-300' : industryLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: industryLight === 'inactive' ? '대기중' : getStatusText(industryLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(marketLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(marketLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(marketLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        marketLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(marketLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(marketLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"시장 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(marketLight === 'inactive' ? 'text-gray-400' : marketLight === 'good' ? 'text-green-300' : marketLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: marketLight === 'inactive' ? '대기중' : getStatusText(marketLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(riskLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        riskLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(riskLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"변동성 리스크\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(riskLight === 'inactive' ? 'text-gray-400' : riskLight === 'good' ? 'text-green-300' : riskLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(neuralLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(neuralLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(neuralLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        neuralLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(neuralLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        lstmLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full border-2 border-blue-500 border-t-transparent animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(neuralLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: lstmLoading ? '딥러닝 기반 예측(LSTM)' : '신경망 예측(LSTM)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(neuralLight === 'inactive' ? 'text-gray-400' : neuralLight === 'good' ? 'text-green-300' : neuralLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: lstmLoading ? '분석중...' : neuralLight === 'inactive' ? '대기중' : getStatusText(neuralLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, undefined),\n                phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"딥러닝 기반 예측(LSTM) 분석 중...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 11\n                }, undefined),\n                showTimeoutMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-600 text-center\",\n                        children: \"이 작업은 시간이 걸립니다... (최대 60초)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-red-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"오류: \",\n                                    predictionError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && !phase1Loading && !phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchStagedPrediction,\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors\",\n                        children: \"다시 시도\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n            lineNumber: 370,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pre-ticker mode: Market indicators display\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-slate-900\",\n                        children: \"시장 현황\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 rounded-full animate-pulse \".concat(loading ? 'bg-yellow-400' : 'bg-green-400')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: loading && indicators.length === 0 ? // 로딩 스켈레톤\n                Array.from({\n                    length: 5\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-slate-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-slate-200 rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 13\n                    }, undefined)) : indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-slate-700\",\n                                        children: indicator.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    getTrendIcon(indicator.trend)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-slate-900\",\n                                        children: indicator.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium \".concat(getChangeColor(indicator.trend)),\n                                        children: indicator.change\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-3 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-2 text-xs text-slate-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: loading ? '업데이트 중...' : lastUpdate ? \"마지막 업데이트: \".concat(lastUpdate) : '20초마다 업데이트'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n        lineNumber: 592,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpeedTraffic, \"xTpOOAn020+5NcZ3QUrHtZi5uK0=\");\n_c = SpeedTraffic;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpeedTraffic);\nvar _c;\n$RefreshReg$(_c, \"SpeedTraffic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SpeedTraffic.tsx\n"));

/***/ })

});