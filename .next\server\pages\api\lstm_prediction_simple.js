"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_prediction_simple";
exports.ids = ["pages/api/lstm_prediction_simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_prediction_simple.ts */ \"(api)/./src/pages/api/lstm_prediction_simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_prediction_simple\",\n        pathname: \"/api/lstm_prediction_simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_prediction_simple.ts":
/*!*************************************************!*\
  !*** ./src/pages/api/lstm_prediction_simple.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Per-symbol mutex using in-memory Map with timestamps\nconst processing = new Map();\n// Circuit breaker pattern - track failures per symbol\nconst failureCount = new Map();\nconst lastFailureTime = new Map();\nconst FAILURE_THRESHOLD = 3;\nconst CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes\n// Cleanup stale processing entries (older than 5 minutes)\nconst cleanupStaleProcessing = ()=>{\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    for (const [symbol, info] of processing.entries()){\n        if (info.active && now - info.startTime > fiveMinutes) {\n            console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);\n            processing.delete(symbol);\n        }\n    }\n};\n// Check if circuit breaker is open for a symbol\nconst isCircuitBreakerOpen = (symbol)=>{\n    const failures = failureCount.get(symbol) || 0;\n    const lastFailure = lastFailureTime.get(symbol) || 0;\n    const now = Date.now();\n    if (failures >= FAILURE_THRESHOLD) {\n        if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {\n            return true; // Circuit breaker is open\n        } else {\n            // Reset circuit breaker after timeout\n            failureCount.delete(symbol);\n            lastFailureTime.delete(symbol);\n            return false;\n        }\n    }\n    return false;\n};\n// Record a failure for circuit breaker\nconst recordFailure = (symbol)=>{\n    const failures = (failureCount.get(symbol) || 0) + 1;\n    failureCount.set(symbol, failures);\n    lastFailureTime.set(symbol, Date.now());\n    console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);\n};\n// Run cleanup every minute\nsetInterval(cleanupStaleProcessing, 60 * 1000);\n// Korean summary mapping\nconst getKoreanSummary = (predictions)=>{\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    if (correctCount === 2) {\n        return \"모두 예측 성공 결과 : green\";\n    } else if (correctCount === 1) {\n        return \"2일 예측 중 1일 예측 실패 결과 : yellow\";\n    } else {\n        return \"모두 예측 실패 결과 : red\";\n    }\n};\n// Traffic light color determination\nconst getTrafficLightColor = (result, processType)=>{\n    if (processType === 'LSTM') {\n        if (result.predictions && Array.isArray(result.predictions)) {\n            const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            if (correctCount === 2) return 'green';\n            if (correctCount === 1) return 'yellow';\n            return 'red';\n        }\n        // Fallback to result_color if available\n        return result.result_color || result.color || 'yellow';\n    } else if (processType === 'MFI') {\n        return result.traffic_light || result.color || 'yellow';\n    }\n    return 'yellow';\n};\n// Calculate LSTM accuracy percentage\nconst calculateLSTMAccuracy = (predictions)=>{\n    if (!Array.isArray(predictions) || predictions.length === 0) return 0;\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    return Math.round(correctCount / predictions.length * 100);\n};\n// Get LSTM pred_prob_up value\nconst getLSTMPredProbUp = (result)=>{\n    if (result.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {\n        return result.predictions[0].pred_prob_up || 0;\n    }\n    return result.pred_prob_up || 0;\n};\n// Get MFI numerical value\nconst getMFIValue = (result)=>{\n    return result.mfi_14 || result.mfi || 0;\n};\n// Get service-specific values for logging and fine-tuning\nconst getBollingerValue = (result)=>{\n    return result.percent_b || result.bollinger_position || result.position || 0;\n};\nconst getRSIValue = (result)=>{\n    return result.rsi_14 || result.rsi || 0;\n};\nconst getIndustryValue = (result)=>{\n    return result.industry || result.industry_sentiment || result.sentiment || 'neutral';\n};\nconst getGARCHValue = (result)=>{\n    return result.var95_pct || result.var_pct || result.volatility_forecast || result.volatility || 0;\n};\n// Get traffic light color from service results\nconst getServiceTrafficLight = (result)=>{\n    return result.traffic_light || result.color || 'yellow';\n};\n// Majority vote logic for technical analysis (MFI, Bollinger, RSI)\nconst getTechnicalMajorityVote = (mfiColor, bollingerColor, rsiColor)=>{\n    const colors = [\n        mfiColor,\n        bollingerColor,\n        rsiColor\n    ];\n    const colorCounts = {\n        green: colors.filter((c)=>c === 'green').length,\n        yellow: colors.filter((c)=>c === 'yellow').length,\n        red: colors.filter((c)=>c === 'red').length\n    };\n    // Return the color with the highest count\n    if (colorCounts.green >= 2) return 'green';\n    if (colorCounts.red >= 2) return 'red';\n    return 'yellow'; // Default to yellow if no majority or all different\n};\n// Execute a process and return its result with enhanced logging\nconst executeProcess = (scriptPath, ticker, processName)=>{\n    return new Promise((resolve, reject)=>{\n        const servicesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services');\n        const startTime = Date.now();\n        console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);\n        const childProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            scriptPath,\n            ticker\n        ], {\n            cwd: servicesDir,\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ],\n            env: {\n                ...process.env,\n                PYTHONUNBUFFERED: '1'\n            }\n        });\n        let stdout = '';\n        let stderr = '';\n        childProcess.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        childProcess.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        childProcess.on('close', (code)=>{\n            const executionTime = Date.now() - startTime;\n            console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);\n            if (code === 0) {\n                try {\n                    // Parse the JSON output from the last line\n                    const lines = stdout.trim().split('\\n').filter((line)=>line.trim());\n                    // Find the line that looks like JSON (starts with '{')\n                    let jsonLine = null;\n                    for(let i = lines.length - 1; i >= 0; i--){\n                        const line = lines[i].trim();\n                        if (line.startsWith('{')) {\n                            jsonLine = line;\n                            break;\n                        }\n                    }\n                    if (!jsonLine) {\n                        throw new Error(`No JSON output found in ${processName} stdout`);\n                    }\n                    const jsonOutput = JSON.parse(jsonLine);\n                    // Enhanced result logging\n                    if (processName === 'LSTM') {\n                        const accuracy = calculateLSTMAccuracy(jsonOutput.predictions || []);\n                        const predProbUp = getLSTMPredProbUp(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');\n                        console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, pred_prob_up: ${predProbUp}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'MFI') {\n                        const mfiValue = getMFIValue(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');\n                        console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'BOLLINGER') {\n                        const bollingerValue = getBollingerValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[BOLLINGER_RESULT] ${ticker} - Percent B: ${bollingerValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'RSI') {\n                        const rsiValue = getRSIValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[RSI_RESULT] ${ticker} - RSI Value: ${rsiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'INDUSTRY') {\n                        const industryValue = getIndustryValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[INDUSTRY_RESULT] ${ticker} - Industry: ${industryValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'GARCH') {\n                        const garchValue = getGARCHValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[GARCH_RESULT] ${ticker} - VaR %: ${garchValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'CAPM') {\n                        const capmBeta = jsonOutput.beta_market || 0;\n                        const capmR2 = jsonOutput.r2_market || 0;\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[CAPM_RESULT] ${ticker} - Beta: ${capmBeta}, R²: ${capmR2}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    }\n                    resolve(jsonOutput);\n                } catch (parseError) {\n                    console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);\n                    reject(new Error(`Failed to parse ${processName} output: ${parseError}`));\n                }\n            } else {\n                console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);\n                console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);\n                reject(new Error(`${processName} process failed with code ${code}`));\n            }\n        });\n        childProcess.on('error', (error)=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);\n            reject(new Error(`${processName} process error: ${error.message}`));\n        });\n        // Set timeout for individual process (60 seconds)\n        setTimeout(()=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);\n            childProcess.kill('SIGTERM');\n            reject(new Error(`${processName} process timed out after 60 seconds`));\n        }, 60000);\n    });\n};\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    // Check circuit breaker\n    if (isCircuitBreakerOpen(ticker)) {\n        return res.status(503).json({\n            error: 'Service temporarily unavailable due to repeated failures',\n            retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)\n        });\n    }\n    // Check if already processing\n    const currentProcessing = processing.get(ticker);\n    if (currentProcessing?.active) {\n        return res.status(429).json({\n            error: 'Already processing this symbol',\n            retryAfter: 15\n        });\n    }\n    // Set processing flag\n    processing.set(ticker, {\n        active: true,\n        startTime: Date.now()\n    });\n    try {\n        console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);\n        // Check if this is a staged execution request\n        const { stage } = req.query;\n        // Paths to service scripts\n        const lstmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_service.py');\n        const mfiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'mfi_service.py');\n        const bollingerScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'bollinger_service.py');\n        const rsiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'rsi_service.py');\n        const industryScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'industry_regression_service.py');\n        const garchScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'garch_service.py');\n        const capmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'capm_service.py');\n        // Staged execution: Phase 1 (Fast services) vs Phase 2 (LSTM)\n        if (stage === 'phase1') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 1 (fast services) for ${ticker}`);\n            // Execute Phase 1: Technical, Industry, Market, Volatility services in parallel\n            const [mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath) ? executeProcess(mfiScriptPath, ticker, 'MFI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n            ]);\n            // Extract results with error handling\n            const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n            const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n            const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n            const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n            const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n            const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n            // Log any service failures\n            if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n            if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n            if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n            if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n            if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n            if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n            // Calculate technical majority vote for traffic light\n            const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n            const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n            const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n            const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n            console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n            // Phase 1 result structure\n            const phase1Result = {\n                phase: 1,\n                mfi: finalMFIResult,\n                bollinger: finalBollingerResult,\n                rsi: finalRSIResult,\n                industry: finalIndustryResult,\n                capm: finalCAPMResult,\n                garch: finalGARCHResult,\n                traffic_lights: {\n                    technical: technicalColor,\n                    industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                    market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                    risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive' // Light 4: Volatility Risk\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 1 completed successfully for ${ticker}`);\n            return res.status(200).json(phase1Result);\n        } else if (stage === 'phase2') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 2 (LSTM) for ${ticker}`);\n            // Validate LSTM script path exists\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n                throw new Error('LSTM service script not found');\n            }\n            // Execute Phase 2: LSTM service only\n            const lstmResult = await executeProcess(lstmScriptPath, ticker, 'LSTM');\n            // Add Korean summary to LSTM result\n            if (lstmResult?.predictions) {\n                lstmResult.summary_ko = getKoreanSummary(lstmResult.predictions);\n            }\n            // Phase 2 result structure\n            const phase2Result = {\n                phase: 2,\n                lstm: lstmResult,\n                traffic_lights: {\n                    neural: lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 2 completed successfully for ${ticker}`);\n            return res.status(200).json(phase2Result);\n        }\n        // Legacy mode: Execute all processes in parallel (backward compatibility)\n        console.log(`[LSTM_SIMPLE_API] Starting legacy mode (all services) for ${ticker}`);\n        // Validate required script paths exist\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n            throw new Error('LSTM service script not found');\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath)) {\n            throw new Error('MFI service script not found');\n        }\n        // Execute all processes in parallel with graceful error handling\n        const [lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n            executeProcess(lstmScriptPath, ticker, 'LSTM'),\n            executeProcess(mfiScriptPath, ticker, 'MFI'),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n        ]);\n        // Extract results with error handling\n        const finalLSTMResult = lstmResult.status === 'fulfilled' ? lstmResult.value : null;\n        const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n        const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n        const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n        const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n        const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n        const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n        // Log any service failures\n        if (lstmResult.status === 'rejected') console.error(`[LSTM_ERROR] ${ticker}:`, lstmResult.reason);\n        if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n        if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n        if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n        if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n        if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n        if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n        // Add Korean summary to LSTM result\n        if (finalLSTMResult?.predictions) {\n            finalLSTMResult.summary_ko = getKoreanSummary(finalLSTMResult.predictions);\n        }\n        // Calculate technical majority vote for traffic light\n        const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n        const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n        const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n        // Note: Fine-tuning data is now saved by SpeedTraffic component after complete analysis\n        // Merge results with enhanced structure (legacy mode)\n        const mergedResult = {\n            lstm: finalLSTMResult,\n            mfi: finalMFIResult,\n            bollinger: finalBollingerResult,\n            rsi: finalRSIResult,\n            industry: finalIndustryResult,\n            capm: finalCAPMResult,\n            garch: finalGARCHResult,\n            traffic_lights: {\n                technical: technicalColor,\n                industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive',\n                neural: finalLSTMResult ? getTrafficLightColor(finalLSTMResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n            }\n        };\n        console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker} with ${Object.keys(mergedResult.traffic_lights).length} traffic lights`);\n        // Return the merged result\n        res.status(200).json(mergedResult);\n    } catch (error) {\n        console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);\n        // Record failure for circuit breaker\n        recordFailure(ticker);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Prediction failed',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    } finally{\n        // Clean up processing flag\n        processing.delete(ticker);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_prediction_simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();