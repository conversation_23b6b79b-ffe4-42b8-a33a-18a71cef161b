/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
    /* 색상 팔레트 */
    --primary: 59 130 246; /* blue-500 */
    --primary-dark: 37 99 235; /* blue-600 */
    --primary-light: 147 197 253; /* blue-300 */

    --secondary: 99 102 241; /* indigo-500 */
    --accent: 168 85 247; /* purple-500 */

    --background: 248 250 252; /* slate-50 */
    --surface: 255 255 255; /* white */
    --surface-dark: 241 245 249; /* slate-100 */

    --text-primary: 15 23 42; /* slate-900 */
    --text-secondary: 71 85 105; /* slate-600 */
    --text-muted: 148 163 184; /* slate-400 */

    --border: 226 232 240; /* slate-200 */
    --border-light: 241 245 249; /* slate-100 */

    --success: 34 197 94; /* green-500 */
    --warning: 245 158 11; /* amber-500 */
    --error: 239 68 68; /* red-500 */

    /* 그림자 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* 반지름 */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
  }

.dark {
    /* 다크 모드 색상 */
    --primary: 96 165 250; /* blue-400 */
    --primary-dark: 59 130 246; /* blue-500 */
    --primary-light: 147 197 253; /* blue-300 */

    --secondary: 129 140 248; /* indigo-400 */
    --accent: 196 181 253; /* purple-400 */

    --background: 15 23 42; /* slate-900 */
    --surface: 30 41 59; /* slate-800 */
    --surface-dark: 51 65 85; /* slate-700 */

    --text-primary: 248 250 252; /* slate-50 */
    --text-secondary: 203 213 225; /* slate-300 */
    --text-muted: 148 163 184; /* slate-400 */

    --border: 51 65 85; /* slate-700 */
    --border-light: 71 85 105; /* slate-600 */
  }

*{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --tw-bg-opacity: 1;
    background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(15 23 42 / var(--tw-text-opacity, 1));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

/* 스크롤바 스타일링 */

::-webkit-scrollbar {
    width: 6px;
  }

::-webkit-scrollbar-track{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

::-webkit-scrollbar-thumb{
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}

::-webkit-scrollbar-thumb:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
/* 글래스모피즘 효과 */
/* 그라디언트 배경 */
.gradient-bg {
    background: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(99 102 241) 100%);
  }
/* 애니메이션 */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
.animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
/* 호버 효과 */
/* 버튼 스타일 */
/* 입력 필드 */
.input-modern{
  border-radius: var(--radius);
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.input-modern::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}
.input-modern{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  animation-duration: 200ms;
}
.input-modern:focus{
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
/* 카드 */
.card{
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1.5rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.inset-0{
  inset: 0px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-bottom-12{
  bottom: -3rem;
}
.-left-12{
  left: -3rem;
}
.-right-12{
  right: -3rem;
}
.-top-12{
  top: -3rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-4{
  bottom: 1rem;
}
.left-0{
  left: 0px;
}
.left-1{
  left: 0.25rem;
}
.left-1\/2{
  left: 50%;
}
.left-2{
  left: 0.5rem;
}
.left-\[50\%\]{
  left: 50%;
}
.right-0{
  right: 0px;
}
.right-1{
  right: 0.25rem;
}
.right-2{
  right: 0.5rem;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-1\.5{
  top: 0.375rem;
}
.top-1\/2{
  top: 50%;
}
.top-3\.5{
  top: 0.875rem;
}
.top-4{
  top: 1rem;
}
.top-\[1px\]{
  top: 1px;
}
.top-\[50\%\]{
  top: 50%;
}
.top-\[60\%\]{
  top: 60%;
}
.top-full{
  top: 100%;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-50{
  z-index: 50;
}
.z-\[100\]{
  z-index: 100;
}
.z-\[1\]{
  z-index: 1;
}
.-m-2{
  margin: -0.5rem;
}
.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3\.5{
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5{
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.-ml-4{
  margin-left: -1rem;
}
.-mt-4{
  margin-top: -1rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-2{
  margin-right: 0.5rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-24{
  margin-top: 6rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-auto{
  margin-top: auto;
}
.block{
  display: block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.size-4{
  width: 1rem;
  height: 1rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-60{
  height: 15rem;
}
.h-64{
  height: 16rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-\[1px\]{
  height: 1px;
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\]{
  height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\]{
  height: var(--radix-select-trigger-height);
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.h-svh{
  height: 100svh;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[300px\]{
  max-height: 300px;
}
.max-h-full{
  max-height: 100%;
}
.max-h-screen{
  max-height: 100vh;
}
.min-h-0{
  min-height: 0px;
}
.min-h-\[60px\]{
  min-height: 60px;
}
.min-h-screen{
  min-height: 100vh;
}
.min-h-svh{
  min-height: 100svh;
}
.w-0{
  width: 0px;
}
.w-1{
  width: 0.25rem;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-20{
  width: 5rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-4{
  width: 1rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-\[--sidebar-width\]{
  width: var(--sidebar-width);
}
.w-\[100px\]{
  width: 100px;
}
.w-\[1px\]{
  width: 1px;
}
.w-auto{
  width: auto;
}
.w-full{
  width: 100%;
}
.w-max{
  width: max-content;
}
.w-px{
  width: 1px;
}
.min-w-0{
  min-width: 0px;
}
.min-w-10{
  min-width: 2.5rem;
}
.min-w-5{
  min-width: 1.25rem;
}
.min-w-8{
  min-width: 2rem;
}
.min-w-9{
  min-width: 2.25rem;
}
.min-w-\[12rem\]{
  min-width: 12rem;
}
.min-w-\[8rem\]{
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\]{
  min-width: var(--radix-select-trigger-width);
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-\[--skeleton-width\]{
  max-width: var(--skeleton-width);
}
.max-w-\[85\%\]{
  max-width: 85%;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-max{
  max-width: max-content;
}
.max-w-md{
  max-width: 28rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.grow-0{
  flex-grow: 0;
}
.basis-full{
  flex-basis: 100%;
}
.caption-bottom{
  caption-side: bottom;
}
.border-collapse{
  border-collapse: collapse;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px{
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-px{
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2{
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes ping{

  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-pointer{
  cursor: pointer;
}
.touch-none{
  touch-action: none;
}
.select-none{
  user-select: none;
}
.resize{
  resize: both;
}
.list-none{
  list-style-type: none;
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-stretch{
  align-items: stretch;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-4{
  gap: 1rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.scroll-smooth{
  scroll-behavior: smooth;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-line{
  white-space: pre-line;
}
.break-words{
  overflow-wrap: break-word;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-\[2px\]{
  border-radius: 2px;
}
.rounded-\[inherit\]{
  border-radius: inherit;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-t-\[10px\]{
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.rounded-tl-sm{
  border-top-left-radius: calc(var(--radius) - 4px);
}
.border{
  border-width: 1px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-\[1\.5px\]{
  border-width: 1.5px;
}
.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-l{
  border-left-width: 1px;
}
.border-r{
  border-right-width: 1px;
}
.border-t{
  border-top-width: 1px;
}
.border-dashed{
  border-style: dashed;
}
.border-\[--color-border\]{
  border-color: var(--color-border);
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-border\/50{
  border-color: hsl(var(--border) / 0.5);
}
.border-destructive{
  border-color: hsl(var(--destructive));
}
.border-destructive\/50{
  border-color: hsl(var(--destructive) / 0.5);
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-500\/30{
  border-color: rgb(107 114 128 / 0.3);
}
.border-green-500\/30{
  border-color: rgb(34 197 94 / 0.3);
}
.border-input{
  border-color: hsl(var(--input));
}
.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-primary{
  border-color: hsl(var(--primary));
}
.border-primary\/50{
  border-color: hsl(var(--primary) / 0.5);
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-500\/20{
  border-color: rgb(239 68 68 / 0.2);
}
.border-red-500\/30{
  border-color: rgb(239 68 68 / 0.3);
}
.border-sidebar-border{
  border-color: hsl(var(--sidebar-border));
}
.border-slate-100{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}
.border-slate-200{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-slate-700{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-yellow-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}
.border-yellow-500\/20{
  border-color: rgb(234 179 8 / 0.2);
}
.border-yellow-500\/30{
  border-color: rgb(234 179 8 / 0.3);
}
.border-l-transparent{
  border-left-color: transparent;
}
.border-r-purple-500{
  --tw-border-opacity: 1;
  border-right-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-t-blue-500{
  --tw-border-opacity: 1;
  border-top-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-\[--color-bg\]{
  background-color: var(--color-bg);
}
.bg-accent{
  background-color: hsl(var(--accent));
}
.bg-amber-400{
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-background{
  background-color: hsl(var(--background));
}
.bg-black\/80{
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-400{
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-border{
  background-color: hsl(var(--border));
}
.bg-card{
  background-color: hsl(var(--card));
}
.bg-destructive{
  background-color: hsl(var(--destructive));
}
.bg-foreground{
  background-color: hsl(var(--foreground));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20{
  background-color: rgb(107 114 128 / 0.2);
}
.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/20{
  background-color: rgb(34 197 94 / 0.2);
}
.bg-muted{
  background-color: hsl(var(--muted));
}
.bg-muted\/50{
  background-color: hsl(var(--muted) / 0.5);
}
.bg-popover{
  background-color: hsl(var(--popover));
}
.bg-primary{
  background-color: hsl(var(--primary));
}
.bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20{
  background-color: hsl(var(--primary) / 0.2);
}
.bg-red-400{
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/10{
  background-color: rgb(239 68 68 / 0.1);
}
.bg-red-500\/20{
  background-color: rgb(239 68 68 / 0.2);
}
.bg-secondary{
  background-color: hsl(var(--secondary));
}
.bg-sidebar{
  background-color: hsl(var(--sidebar-background));
}
.bg-sidebar-border{
  background-color: hsl(var(--sidebar-border));
}
.bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-slate-200{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.bg-slate-50{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-slate-50\/50{
  background-color: rgb(248 250 252 / 0.5);
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400{
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/10{
  background-color: rgb(234 179 8 / 0.1);
}
.bg-yellow-500\/20{
  background-color: rgb(234 179 8 / 0.2);
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-100{
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-400{
  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-100{
  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(241 245 249 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50{
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50\/30{
  --tw-gradient-from: rgb(248 250 252 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-800{
  --tw-gradient-from: #1e293b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-blue-500{
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-indigo-600{
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-purple-100{
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}
.to-slate-200{
  --tw-gradient-to: #e2e8f0 var(--tw-gradient-to-position);
}
.to-slate-900{
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}
.to-white{
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.fill-current{
  fill: currentColor;
}
.fill-primary{
  fill: hsl(var(--primary));
}
.object-cover{
  object-fit: cover;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-12{
  padding: 3rem;
}
.p-2{
  padding: 0.5rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.p-\[1px\]{
  padding: 1px;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-2\.5{
  padding-left: 0.625rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-8{
  padding-left: 2rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-2\.5{
  padding-right: 0.625rem;
}
.pr-6{
  padding-right: 1.5rem;
}
.pr-8{
  padding-right: 2rem;
}
.pr-9{
  padding-right: 2.25rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.align-middle{
  vertical-align: middle;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[0\.8rem\]{
  font-size: 0.8rem;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.tabular-nums{
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-none{
  line-height: 1;
}
.leading-relaxed{
  line-height: 1.625;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-accent-foreground{
  color: hsl(var(--accent-foreground));
}
.text-amber-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-card-foreground{
  color: hsl(var(--card-foreground));
}
.text-current{
  color: currentColor;
}
.text-cyan-400{
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.text-destructive{
  color: hsl(var(--destructive));
}
.text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}
.text-foreground{
  color: hsl(var(--foreground));
}
.text-foreground\/50{
  color: hsl(var(--foreground) / 0.5);
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-300{
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.text-orange-400{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}
.text-primary{
  color: hsl(var(--primary));
}
.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.text-purple-400{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}
.text-sidebar-foreground{
  color: hsl(var(--sidebar-foreground));
}
.text-sidebar-foreground\/70{
  color: hsl(var(--sidebar-foreground) / 0.7);
}
.text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-300{
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline-offset-4{
  text-underline-offset: 4px;
}
.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-5{
  opacity: 0.05;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-90{
  opacity: 0.9;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\]{
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-ring{
  --tw-ring-color: hsl(var(--ring));
}
.ring-sidebar-ring{
  --tw-ring-color: hsl(var(--sidebar-ring));
}
.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[left\2c right\2c width\]{
  transition-property: left,right,width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[margin\2c opa\]{
  transition-property: margin,opa;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c height\2c padding\]{
  transition-property: width,height,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\]{
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-1000{
  transition-duration: 1000ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-700{
  transition-duration: 700ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  transition-timing-function: linear;
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@keyframes enter{

  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{

  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0{
  --tw-enter-opacity: 0;
}
.zoom-in-95{
  --tw-enter-scale: .95;
}
.duration-1000{
  animation-duration: 1000ms;
}
.duration-200{
  animation-duration: 200ms;
}
.duration-300{
  animation-duration: 300ms;
}
.duration-700{
  animation-duration: 700ms;
}
.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear{
  animation-timing-function: linear;
}
.ease-out{
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* ───────── 모던 디자인 시스템 ───────── */

/* 기본 스타일 */
/* ───── 번개(Static-Route Indicator) 완전 숨김 ───── */
[data-nextjs-route-indicator],
[data-next-route-indicator],
#next-route-indicator {
  display: none !important;
}

/* ───── TradingView 차트 관련 스타일 ───── */
/* TradingView 워터마크 및 로고 완전 제거 */
.tv-lightweight-charts table tr td div[style*="pointer-events"],
.tv-lightweight-charts table tr td div[style*="position: absolute"],
.tv-lightweight-charts div[style*="pointer-events: none"],
.tv-lightweight-charts div[style*="position: absolute"][style*="left: 4px"],
.tv-lightweight-charts div[style*="position: absolute"][style*="right: 4px"],
.tv-lightweight-charts div[style*="position: absolute"][style*="bottom: 4px"],
.tv-lightweight-charts div[style*="position: absolute"][style*="top: 4px"],
.tv-lightweight-charts div[style*="position: absolute"][style*="z-index"],
.tv-lightweight-charts div[style*="position: absolute"][style*="cursor: pointer"],
.tv-lightweight-charts a[href*="tradingview"],
.tv-lightweight-charts a[href*="TradingView"],
.tv-lightweight-charts div[style*="font-family"][style*="position: absolute"],
.tv-lightweight-charts div[style*="font-size: 11px"],
.tv-lightweight-charts div[style*="font-size: 12px"][style*="position: absolute"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* TradingView 차트 컨테이너 스타일 개선 */
.tv-lightweight-charts {
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

/* 차트 영역 스타일 */
.tv-lightweight-charts table {
  border-radius: 0.5rem;
  width: 100% !important;
  height: 100% !important;
}

/* 차트 캔버스 스타일 */
.tv-lightweight-charts canvas {
  border-radius: 0.5rem;
}

/* ───── 커스텀 컴포넌트 스타일 ───────── */

/* ───── 키프레임 애니메이션 ───────── */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* ───── 추가 유틸리티 클래스 ───────── */
.file\:border-0::file-selector-button{
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}
.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button{
  font-weight: 500;
}
.file\:text-foreground::file-selector-button{
  color: hsl(var(--foreground));
}
.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:-inset-2::after{
  content: var(--tw-content);
  inset: -0.5rem;
}
.after\:inset-y-0::after{
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}
.after\:w-1::after{
  content: var(--tw-content);
  width: 0.25rem;
}
.after\:w-\[2px\]::after{
  content: var(--tw-content);
  width: 2px;
}
.after\:-translate-x-1\/2::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.first\:rounded-l-md:first-child{
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.first\:border-l:first-child{
  border-left-width: 1px;
}
.last\:rounded-r-md:last-child{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.focus-within\:relative:focus-within{
  position: relative;
}
.focus-within\:z-20:focus-within{
  z-index: 20;
}
.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}
.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/80:hover{
  background-color: hsl(var(--destructive) / 0.8);
}
.hover\:bg-destructive\/90:hover{
  background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-gray-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted:hover{
  background-color: hsl(var(--muted));
}
.hover\:bg-muted\/50:hover{
  background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary:hover{
  background-color: hsl(var(--primary));
}
.hover\:bg-primary\/80:hover{
  background-color: hsl(var(--primary) / 0.8);
}
.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-secondary:hover{
  background-color: hsl(var(--secondary));
}
.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-sidebar-accent:hover{
  background-color: hsl(var(--sidebar-accent));
}
.hover\:bg-slate-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}
.hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}
.hover\:text-muted-foreground:hover{
  color: hsl(var(--muted-foreground));
}
.hover\:text-primary-foreground:hover{
  color: hsl(var(--primary-foreground));
}
.hover\:text-sidebar-accent-foreground:hover{
  color: hsl(var(--sidebar-accent-foreground));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover{
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:after\:bg-sidebar-border:hover::after{
  content: var(--tw-content);
  background-color: hsl(var(--sidebar-border));
}
.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}
.focus\:bg-primary:focus{
  background-color: hsl(var(--primary));
}
.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}
.focus\:text-primary-foreground:focus{
  color: hsl(var(--primary-foreground));
}
.focus\:opacity-100:focus{
  opacity: 1;
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-sidebar-ring:focus-visible{
  --tw-ring-color: hsl(var(--sidebar-ring));
}
.focus-visible\:ring-offset-1:focus-visible{
  --tw-ring-offset-width: 1px;
}
.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}
.focus-visible\:ring-offset-background:focus-visible{
  --tw-ring-offset-color: hsl(var(--background));
}
.active\:bg-sidebar-accent:active{
  background-color: hsl(var(--sidebar-accent));
}
.active\:text-sidebar-accent-foreground:active{
  color: hsl(var(--sidebar-accent-foreground));
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:bg-blue-300:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-gray-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group.destructive .group-\[\.destructive\]\:border-muted\/40{
  border-color: hsl(var(--muted) / 0.4);
}
.group.toaster .group-\[\.toaster\]\:border-border{
  border-color: hsl(var(--border));
}
.group.toast .group-\[\.toast\]\:bg-muted{
  background-color: hsl(var(--muted));
}
.group.toast .group-\[\.toast\]\:bg-primary{
  background-color: hsl(var(--primary));
}
.group.toaster .group-\[\.toaster\]\:bg-background{
  background-color: hsl(var(--background));
}
.group.destructive .group-\[\.destructive\]\:text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.group.toast .group-\[\.toast\]\:text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.group.toast .group-\[\.toast\]\:text-primary-foreground{
  color: hsl(var(--primary-foreground));
}
.group.toaster .group-\[\.toaster\]\:text-foreground{
  color: hsl(var(--foreground));
}
.group.toaster .group-\[\.toaster\]\:shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover{
  border-color: hsl(var(--destructive) / 0.3);
}
.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover{
  background-color: hsl(var(--destructive));
}
.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover{
  color: hsl(var(--destructive-foreground));
}
.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover{
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus{
  --tw-ring-color: hsl(var(--destructive));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus{
  --tw-ring-offset-color: #dc2626;
}
.peer\/menu-button:hover ~ .peer-hover\/menu-button\:text-sidebar-accent-foreground{
  color: hsl(var(--sidebar-accent-foreground));
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}
.has-\[\[data-variant\=inset\]\]\:bg-sidebar:has([data-variant=inset]){
  background-color: hsl(var(--sidebar-background));
}
.has-\[\:disabled\]\:opacity-50:has(:disabled){
  opacity: 0.5;
}
.group\/menu-item:has([data-sidebar=menu-action]) .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8{
  padding-right: 2rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"]{
  pointer-events: none;
}
.aria-disabled\:opacity-50[aria-disabled="true"]{
  opacity: 0.5;
}
.aria-selected\:bg-accent[aria-selected="true"]{
  background-color: hsl(var(--accent));
}
.aria-selected\:bg-accent\/50[aria-selected="true"]{
  background-color: hsl(var(--accent) / 0.5);
}
.aria-selected\:text-accent-foreground[aria-selected="true"]{
  color: hsl(var(--accent-foreground));
}
.aria-selected\:text-muted-foreground[aria-selected="true"]{
  color: hsl(var(--muted-foreground));
}
.aria-selected\:opacity-100[aria-selected="true"]{
  opacity: 1;
}
.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"]{
  pointer-events: none;
}
.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}
.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"]{
  height: 1px;
}
.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"]{
  width: 100%;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"]{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"]{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=checked\]\:translate-x-4[data-state="checked"]{
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"]{
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"]{
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes accordion-up{

  from{
    height: var(--radix-accordion-content-height);
  }

  to{
    height: 0;
  }
}
.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"]{
  animation: accordion-up 0.2s ease-out;
}
@keyframes accordion-down{

  from{
    height: 0;
  }

  to{
    height: var(--radix-accordion-content-height);
  }
}
.data-\[state\=open\]\:animate-accordion-down[data-state="open"]{
  animation: accordion-down 0.2s ease-out;
}
.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"]{
  flex-direction: column;
}
.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"]{
  background-color: hsl(var(--sidebar-accent));
}
.data-\[active\]\:bg-accent\/50[data-active]{
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[selected\=true\]\:bg-accent[data-selected="true"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=active\]\:bg-background[data-state="active"]{
  background-color: hsl(var(--background));
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background-color: hsl(var(--primary));
}
.data-\[state\=on\]\:bg-accent[data-state="on"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent\/50[data-state="open"]{
  background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=open\]\:bg-secondary[data-state="open"]{
  background-color: hsl(var(--secondary));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"]{
  background-color: hsl(var(--muted));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"]{
  background-color: hsl(var(--input));
}
.data-\[active\=true\]\:font-medium[data-active="true"]{
  font-weight: 500;
}
.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"]{
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"]{
  color: hsl(var(--accent-foreground));
}
.data-\[state\=active\]\:text-foreground[data-state="active"]{
  color: hsl(var(--foreground));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"]{
  color: hsl(var(--primary-foreground));
}
.data-\[state\=on\]\:text-accent-foreground[data-state="on"]{
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-accent-foreground[data-state="open"]{
  color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"]{
  opacity: 0.5;
}
.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"]{
  opacity: 1;
}
.data-\[state\=active\]\:shadow[data-state="active"]{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[swipe\=move\]\:transition-none[data-swipe="move"]{
  transition-property: none;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"]{
  transition-duration: 500ms;
}
.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=visible\]\:animate-in[data-state="visible"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=hidden\]\:animate-out[data-state="hidden"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[swipe\=end\]\:animate-out[data-swipe="end"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"]{
  --tw-enter-opacity: 0;
}
.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"]{
  --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-80[data-state="closed"]{
  --tw-exit-opacity: 0.8;
}
.data-\[state\=hidden\]\:fade-out[data-state="hidden"]{
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}
.data-\[state\=visible\]\:fade-in[data-state="visible"]{
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-90[data-state="open"]{
  --tw-enter-scale: .9;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}
.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"]{
  --tw-enter-translate-x: 13rem;
}
.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"]{
  --tw-enter-translate-x: -13rem;
}
.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"]{
  --tw-exit-translate-x: 13rem;
}
.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"]{
  --tw-exit-translate-x: -13rem;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"]{
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"]{
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"]{
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"]{
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"]{
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"]{
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"]{
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"]{
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"]{
  --tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"]{
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"]{
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"]{
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"]{
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"]{
  --tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"]{
  --tw-enter-translate-y: -100%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"]{
  animation-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"]{
  animation-duration: 500ms;
}
.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  left: 0px;
}
.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  height: 0.25rem;
}
.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  width: 100%;
}
.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after{
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=open\]\:hover\:bg-sidebar-accent:hover[data-state="open"]{
  background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground:hover[data-state="open"]{
  color: hsl(var(--sidebar-accent-foreground));
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]{
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]{
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4{
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0{
  left: 0px;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8{
  margin-top: -2rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden{
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-8{
  width: 2rem !important;
  height: 2rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\]{
  width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\]{
  width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\]{
  width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:w-0{
  width: 0px;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:overflow-hidden{
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg{
  border-radius: var(--radius);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border{
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r{
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l{
  border-left-width: 1px;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border-sidebar-border{
  border-color: hsl(var(--sidebar-border));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0{
  padding: 0px !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-2{
  padding: 0.5rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0{
  opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:after\:left-full::after{
  content: var(--tw-content);
  left: 100%;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:hover\:bg-sidebar:hover{
  background-color: hsl(var(--sidebar-background));
}
.peer\/menu-button[data-size="default"] ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5{
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"] ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5{
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"] ~ .peer-data-\[size\=sm\]\/menu-button\:top-1{
  top: 0.25rem;
}
.peer[data-variant="inset"] ~ .peer-data-\[variant\=inset\]\:min-h-\[calc\(100svh-theme\(spacing\.4\)\)\]{
  min-height: calc(100svh - 1rem);
}
.peer\/menu-button[data-active="true"] ~ .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground{
  color: hsl(var(--sidebar-accent-foreground));
}
.dark\:border-destructive:is(.dark *){
  border-color: hsl(var(--destructive));
}
@media (min-width: 640px){

  .sm\:bottom-0{
    bottom: 0px;
  }

  .sm\:right-0{
    right: 0px;
  }

  .sm\:top-auto{
    top: auto;
  }

  .sm\:mb-3{
    margin-bottom: 0.75rem;
  }

  .sm\:mt-0{
    margin-top: 0px;
  }

  .sm\:block{
    display: block;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:h-8{
    height: 2rem;
  }

  .sm\:h-80{
    height: 20rem;
  }

  .sm\:h-\[32rem\]{
    height: 32rem;
  }

  .sm\:w-8{
    width: 2rem;
  }

  .sm\:max-w-\[80\%\]{
    max-width: 80%;
  }

  .sm\:max-w-sm{
    max-width: 24rem;
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:flex-col{
    flex-direction: column;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:gap-2\.5{
    gap: 0.625rem;
  }

  .sm\:gap-4{
    gap: 1rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-y-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg{
    border-radius: var(--radius);
  }

  .sm\:rounded-xl{
    border-radius: 0.75rem;
  }

  .sm\:p-3{
    padding: 0.75rem;
  }

  .sm\:p-4{
    padding: 1rem;
  }

  .sm\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:text-left{
    text-align: left;
  }

  .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"]{
    --tw-enter-translate-y: 100%;
  }
}
@media (min-width: 768px){

  .md\:absolute{
    position: absolute;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\]{
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:max-w-\[420px\]{
    max-width: 420px;
  }

  .md\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:opacity-0{
    opacity: 0;
  }

  .after\:md\:hidden::after{
    content: var(--tw-content);
    display: none;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2{
    margin: 0.5rem;
  }

  .peer[data-state="collapsed"][data-variant="inset"] ~ .md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2{
    margin-left: 0.5rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0{
    margin-left: 0px;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl{
    border-radius: 0.75rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow{
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1024px){

  .lg\:block{
    display: block;
  }

  .lg\:hidden{
    display: none;
  }
}
.\[\&\+div\]\:text-xs+div{
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(>.day-range-end){
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(>.day-range-start){
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]){
  border-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]){
  background-color: hsl(var(--accent));
}
.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child{
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside){
  background-color: hsl(var(--accent) / 0.5);
}
.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end){
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]){
  padding-right: 0px;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox]{
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>button\]\:hidden>button{
  display: none;
}
.\[\&\>span\:last-child\]\:truncate>span:last-child{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1>span{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div{
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute>svg{
  position: absolute;
}
.\[\&\>svg\]\:left-4>svg{
  left: 1rem;
}
.\[\&\>svg\]\:top-4>svg{
  top: 1rem;
}
.\[\&\>svg\]\:size-4>svg{
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:h-2\.5>svg{
  height: 0.625rem;
}
.\[\&\>svg\]\:h-3>svg{
  height: 0.75rem;
}
.\[\&\>svg\]\:h-3\.5>svg{
  height: 0.875rem;
}
.\[\&\>svg\]\:w-2\.5>svg{
  width: 0.625rem;
}
.\[\&\>svg\]\:w-3>svg{
  width: 0.75rem;
}
.\[\&\>svg\]\:w-3\.5>svg{
  width: 0.875rem;
}
.\[\&\>svg\]\:shrink-0>svg{
  flex-shrink: 0;
}
.\[\&\>svg\]\:text-destructive>svg{
  color: hsl(var(--destructive));
}
.\[\&\>svg\]\:text-foreground>svg{
  color: hsl(var(--foreground));
}
.\[\&\>svg\]\:text-muted-foreground>svg{
  color: hsl(var(--muted-foreground));
}
.\[\&\>svg\]\:text-sidebar-accent-foreground>svg{
  color: hsl(var(--sidebar-accent-foreground));
}
.\[\&\>svg\~\*\]\:pl-7>svg~*{
  padding-left: 1.75rem;
}
.\[\&\>tr\]\:last\:border-b-0:last-child>tr{
  border-bottom-width: 0px;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text{
  fill: hsl(var(--muted-foreground));
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke='#ccc']{
  stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke='#fff']{
  stroke: transparent;
}
.\[\&_\.recharts-layer\]\:outline-none .recharts-layer{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke='#ccc']{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector{
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{
  fill: hsl(var(--muted));
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke='#ccc']{
  stroke: hsl(var(--border));
}
.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke='#fff']{
  stroke: transparent;
}
.\[\&_\.recharts-sector\]\:outline-none .recharts-sector{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{
  font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{
  color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{
  padding-top: 0px;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{
  width: 1.25rem;
}
.\[\&_p\]\:leading-relaxed p{
  line-height: 1.625;
}
.\[\&_svg\]\:pointer-events-none svg{
  pointer-events: none;
}
.\[\&_svg\]\:size-4 svg{
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\]\:shrink-0 svg{
  flex-shrink: 0;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child{
  border-width: 0px;
}
.\[\&_tr\]\:border-b tr{
  border-bottom-width: 1px;
}
[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2{
  right: -0.5rem;
}
[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize{
  cursor: e-resize;
}
[data-side=left] .\[\[data-side\=left\]_\&\]\:cursor-w-resize{
  cursor: w-resize;
}
[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2{
  left: -0.5rem;
}
[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize{
  cursor: w-resize;
}
[data-side=right] .\[\[data-side\=right\]_\&\]\:cursor-e-resize{
  cursor: e-resize;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

