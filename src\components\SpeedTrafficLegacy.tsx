import React, { useState, useEffect, useRef } from 'react';
import { fetchLSTMPrediction, LSTMProgressCallback } from '../utils/lstm_client';

interface MarketIndicator {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  color: string;
}

interface TrafficLight {
  label: string;
  status: 'good' | 'warning' | 'danger';
  description: string;
}

interface SpeedTrafficProps {
  symbol?: string;
}

const SpeedTrafficLegacy: React.FC<SpeedTrafficProps> = ({ symbol }) => {
  // Holds reference to polling interval so we can clear it
  const pollingIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [indicators, setIndicators] = useState<MarketIndicator[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Removed old trafficLights state - now using dual lights

  // 실제 시장 데이터 가져오기
  const fetchMarketData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/market_data');
      const result = await response.json();

      if (result.success && result.data) {
        const formattedData = result.data.map((item: any) => ({
          label: item.label,
          value: item.price.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          }),
          change: `${item.changePercent >= 0 ? '+' : ''}${item.changePercent.toFixed(2)}%`,
          trend: item.trend,
          color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'
        }));

        setIndicators(formattedData);
        setLastUpdate(new Date().toLocaleTimeString('ko-KR'));
      }
    } catch (error) {
      console.error('Failed to fetch market data:', error);
      // 에러 시 fallback 데이터 사용
      setIndicators([
        { label: 'S&P 500', value: '4,567.89', change: '+1.2%', trend: 'up', color: 'green' },
        { label: '나스닥', value: '14,234.56', change: '+0.8%', trend: 'up', color: 'green' },
        { label: '다우존스', value: '34,567.12', change: '-0.3%', trend: 'down', color: 'red' },
        { label: 'VIX', value: '18.45', change: '-2.1%', trend: 'down', color: 'yellow' },
        { label: '달러/원', value: '1,327.50', change: '+0.5%', trend: 'up', color: 'green' },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Enhanced LSTM + MFI 기반 신호등 상태 업데이트 with SSE
  const [lstmLoading, setLstmLoading] = useState(false);
  const [mfiLoading, setMfiLoading] = useState(false);
  const [lstmError, setLstmError] = useState<string | null>(null);
  const [lstmProgressMessage, setLstmProgressMessage] = useState<string>('');
  const [mfiProgressMessage, setMfiProgressMessage] = useState<string>('');
  const [showLongTaskMessage, setShowLongTaskMessage] = useState(false);
  const [lastRequestTime, setLastRequestTime] = useState<number>(0);

  // 5-Light traffic light system states
  const [forecastLight, setForecastLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [technicalLight, setTechnicalLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [sentimentLight, setSentimentLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [volumeLight, setVolumeLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');
  const [riskLight, setRiskLight] = useState<'good' | 'warning' | 'danger' | 'inactive'>('inactive');

  // Single-flight guard
  const inFlight = useRef(false);

  // Convert result color to traffic light status
  const resultColorToStatus = (color: string): 'good' | 'warning' | 'danger' => {
    switch (color?.toLowerCase()) {
      case 'green':
      case 'good':
        return 'good';
      case 'yellow':
      case 'warning':
        return 'warning';
      case 'red':
      case 'danger':
        return 'danger';
      default:
        return 'warning';
    }
  };

  // Extract color string from various possible schemas
  const getColorFromResult = (obj: any): string | undefined => {
    console.log('[SpeedTraffic] getColorFromResult input:', JSON.stringify(obj, null, 2));

    if (!obj) {
      console.log('[SpeedTraffic] getColorFromResult: obj is null/undefined');
      return undefined;
    }

    if (typeof obj === 'string') {
      console.log('[SpeedTraffic] getColorFromResult: obj is string:', obj);
      return obj;
    }

    if (obj.result_color) {
      console.log('[SpeedTraffic] getColorFromResult: found result_color:', obj.result_color);
      return obj.result_color;
    }

    if (obj.traffic_light) {
      console.log('[SpeedTraffic] getColorFromResult: found traffic_light:', obj.traffic_light);
      return obj.traffic_light;
    }

    if (obj.color) {
      console.log('[SpeedTraffic] getColorFromResult: found color:', obj.color);
      return obj.color;
    }

    if (obj.shock_level) {
      console.log('[SpeedTraffic] getColorFromResult: found shock_level:', obj.shock_level);
      switch (obj.shock_level) {
        case 'none':
          return 'green';
        case 'minor':
          return 'yellow';
        case 'major':
          return 'red';
        default:
          return undefined;
      }
    }

    console.log('[SpeedTraffic] getColorFromResult: no color found');
    return undefined;
  };

  // Debug function to test traffic light updates
  const testTrafficLights = () => {
    console.log('[SpeedTraffic] Testing traffic light updates...');
    setForecastLight('good');
    setTechnicalLight('warning');
    setSentimentLight('danger');
    setVolumeLight('good');
    setRiskLight('warning');
    console.log('[SpeedTraffic] Test traffic lights set');
  };

  const fetchLSTMPredictionData = async () => {
    if (!symbol || inFlight.current) return;

    // Prevent too frequent requests (minimum 10 seconds between requests)
    const now = Date.now();
    if (now - lastRequestTime < 10000) {
      console.log('LSTM request throttled - too frequent');
      return;
    }

    try {
      // Set single-flight guard
      inFlight.current = true;
      setLastRequestTime(now);

      // Set all lights to inactive/grey state when processing starts
      setForecastLight('inactive');
      setTechnicalLight('inactive');
      setSentimentLight('inactive');
      setVolumeLight('inactive');
      setRiskLight('inactive');
      setLstmLoading(true);
      setMfiLoading(true);
      setLstmError(null);
      setLstmProgressMessage('');
      setMfiProgressMessage('');
      setShowLongTaskMessage(false);

      // Start 20-second timer for Korean timeout message
      const longTaskTimer = setTimeout(() => {
        setShowLongTaskMessage(true);
      }, 20000);

      // Set up SSE callbacks
      const callbacks: LSTMProgressCallback = {
        onProgress: (message: string) => {
          console.log('[SpeedTraffic] onProgress received message:', message);

          if (message.startsWith('lstm:')) {
            setLstmProgressMessage(message.substring(5)); // Remove 'lstm:' prefix
          } else if (message === 'lstm working') {
            setLstmProgressMessage('LSTM 모델 학습 중...');
          } else if (message === 'mfi working') {
            setMfiProgressMessage('MFI 기술적 분석 중...');
          } else if (message.startsWith('lstm_complete:')) {
            // Handle LSTM completion immediately
            try {
              const completionData = JSON.parse(message.substring(14)); // Remove 'lstm_complete:' prefix
              console.log('[SpeedTraffic] LSTM completed:', completionData);

              if (completionData.lstm) {
                console.log('[SpeedTraffic] LSTM completion data structure:', JSON.stringify(completionData.lstm, null, 2));
                const lstmColor = getColorFromResult(completionData.lstm.predictions?.[0]) || getColorFromResult(completionData.lstm);
                console.log('[SpeedTraffic] Extracted LSTM color:', lstmColor);
                if (lstmColor) {
                  const newStatus = resultColorToStatus(lstmColor);
                  console.log('[SpeedTraffic] Setting LSTM forecast light to:', newStatus);
                  setForecastLight(newStatus);
                  setLstmLoading(false);
                  console.log('[SpeedTraffic] LSTM forecast light state updated and loading stopped');
                } else {
                  console.warn('[SpeedTraffic] No LSTM color found in completion data');
                }
              }
            } catch (error) {
              console.error('[SpeedTraffic] Error parsing LSTM completion:', error);
            }
          } else if (message.startsWith('mfi_complete:')) {
            // Handle MFI completion immediately
            try {
              const completionData = JSON.parse(message.substring(13)); // Remove 'mfi_complete:' prefix
              console.log('[SpeedTraffic] MFI completed:', completionData);

              if (completionData.mfi) {
                console.log('[SpeedTraffic] MFI completion data structure:', JSON.stringify(completionData.mfi, null, 2));
                const mfiColor = getColorFromResult(completionData.mfi);
                console.log('[SpeedTraffic] Extracted MFI color:', mfiColor);
                if (mfiColor) {
                  const newStatus = resultColorToStatus(mfiColor);
                  console.log('[SpeedTraffic] Setting MFI technical light to:', newStatus);
                  setTechnicalLight(newStatus);
                  setMfiLoading(false);
                  console.log('[SpeedTraffic] MFI technical light state updated and loading stopped');
                } else {
                  console.warn('[SpeedTraffic] No MFI color found in completion data');
                }
              }
            } catch (error) {
              console.error('[SpeedTraffic] Error parsing MFI completion:', error);
            }
          }
        },
        onDone: (result: any) => {
          clearTimeout(longTaskTimer);

          console.log('[SpeedTraffic] Final result received:', JSON.stringify(result, null, 2));

          // Handle any remaining LSTM results (fallback if progressive events didn't work)
          console.log('[SpeedTraffic] Current forecastLight state:', forecastLight);
          console.log('[SpeedTraffic] Current technicalLight state:', technicalLight);

          if (result.lstm && forecastLight === 'inactive') {
            console.log('[SpeedTraffic] Processing LSTM fallback data:', JSON.stringify(result.lstm, null, 2));

            let lstmColor: string | undefined;
            if (Array.isArray(result.lstm.predictions) && result.lstm.predictions.length > 0) {
              lstmColor = getColorFromResult(result.lstm.predictions[0]);
              console.log('[SpeedTraffic] LSTM color from predictions[0]:', lstmColor);
            }
            if (!lstmColor) {
              lstmColor = getColorFromResult(result.lstm);
              console.log('[SpeedTraffic] LSTM color from fallback:', lstmColor);
            }

            if (lstmColor) {
              console.log('[SpeedTraffic] Setting forecast light to:', resultColorToStatus(lstmColor));
              setForecastLight(resultColorToStatus(lstmColor));
            }
          }

          // Handle any remaining MFI results (fallback if progressive events didn't work)
          if (result.mfi && technicalLight === 'inactive') {
            const mfiColor = getColorFromResult(result.mfi);
            console.log('[SpeedTraffic] Processing MFI fallback data, color:', mfiColor);

            if (mfiColor) {
              console.log('[SpeedTraffic] Setting technical light to:', resultColorToStatus(mfiColor));
              setTechnicalLight(resultColorToStatus(mfiColor));
            }
          }

          // TODO: Implement additional light logic based on extended data
          // For now, set placeholder values for demonstration
          setSentimentLight('warning'); // Placeholder - implement sentiment analysis
          setVolumeLight('good'); // Placeholder - implement volume analysis
          setRiskLight('danger'); // Placeholder - implement risk analysis

          // Stop any remaining loading states
          setMfiLoading(false);
          setLstmLoading(false);
          inFlight.current = false;
        },
        onError: (error: string) => {
          clearTimeout(longTaskTimer);
          setLstmError(error);
          setForecastLight('inactive');
          setTechnicalLight('inactive');
          setSentimentLight('inactive');
          setVolumeLight('inactive');
          setRiskLight('inactive');
          setLstmLoading(false);
          setMfiLoading(false);
          inFlight.current = false;

          // Log error for debugging
          console.error('LSTM prediction error:', error);
        }
      };

      await fetchLSTMPrediction(symbol, callbacks);

    } catch (error) {
      console.error('LSTM prediction error:', error);
      setLstmError('LSTM 서비스 연결 실패');
      setForecastLight('inactive');
      setTechnicalLight('inactive');
      setSentimentLight('inactive');
      setVolumeLight('inactive');
      setRiskLight('inactive');
      setLstmLoading(false);
      setMfiLoading(false);
      inFlight.current = false;
      // Stop polling if error encountered
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    }

    // Safety timeout to prevent stuck loading states (3 minutes)
    setTimeout(() => {
      if (inFlight.current) {
        console.warn('LSTM request stuck - forcing cleanup');
        setLstmLoading(false);
        setMfiLoading(false);
        inFlight.current = false;
        setLstmError('요청 시간 초과');
        // Stop polling if error encountered
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      }
    }, 180000);
  };

  

  useEffect(() => {
    if (symbol) {
      // 초기 LSTM 예측 실행 (one-time analysis only)
      fetchLSTMPredictionData();

      // DISABLED: 30초마다 LSTM 예측 업데이트 - for one-time analysis only
      // if (!pollingIntervalRef.current) {
      //   pollingIntervalRef.current = setInterval(() => {
      //     if (!inFlight.current && !lstmLoading && !mfiLoading) {
      //       fetchLSTMPredictionData();
      //     }
      //   }, 30000);
      // }

      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      };
    }
  }, [symbol]); // Remove lstmLoading dependency to prevent loops

  // 컴포넌트 마운트 시 초기 데이터 로드
  useEffect(() => {
    fetchMarketData();
  }, []);

  // 20초마다 실제 시장 데이터 업데이트
  useEffect(() => {
    if (!symbol) { // 시장 현황 모드일 때만
      const interval = setInterval(() => {
        fetchMarketData();
      }, 20000); // 20초마다 업데이트

      return () => clearInterval(interval);
    }
  }, [symbol]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
          </svg>
        );
      case 'down':
        return (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        );
    }
  };

  const getChangeColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-slate-500';
    }
  };

  const getTrafficLightColor = (status: 'good' | 'warning' | 'danger') => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'danger': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusText = (status: 'good' | 'warning' | 'danger') => {
    switch (status) {
      case 'good': return '양호';
      case 'warning': return '보통';
      case 'danger': return '주의';
      default: return '분석중';
    }
  };

  // 신호등 모드 (특정 종목 선택 시)
  if (symbol) {
    return (
      <div className="space-y-4 max-w-full overflow-hidden">
        {/* 헤더 */}
        <div className="text-center">
          <h3 className="font-semibold text-slate-900 mb-1">SpeedTraffic™</h3>
          <p className="text-sm text-slate-600">{symbol} 투자 적격성</p>
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse mx-auto mt-2"></div>
        </div>

        {/* 5-Light 신호등 시스템 */}
        <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-xl border border-slate-700">
          <div className="space-y-4">
            {/* Forecast Light (LSTM) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      forecastLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(forecastLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      forecastLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {forecastLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(forecastLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    forecastLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Forecast
                    <span className="ml-2 text-xs text-blue-400 font-normal">
                      {lstmLoading ? '분석중...' : 'LSTM'}
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  forecastLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  forecastLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  forecastLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {forecastLight === 'inactive' ? '대기중' : getStatusText(forecastLight)}
                </span>
              </div>

              {/* LSTM Loading Spinner with Progress */}
              {lstmLoading && (
                <div className="mt-3 px-10 max-w-full overflow-hidden">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                    <span className="text-xs text-gray-400">
                      {lstmProgressMessage || 'LSTM 모델 학습 중...'}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Technical Light (MFI) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      technicalLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {technicalLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(technicalLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Technical
                    <span className="ml-2 text-xs text-orange-400 font-normal">
                      {mfiLoading ? '분석중...' : 'MFI-14'}
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  technicalLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  technicalLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  technicalLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)}
                </span>
              </div>

              {/* MFI Loading Spinner with Progress */}
              {mfiLoading && (
                <div className="mt-3 px-10 max-w-full overflow-hidden">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-orange-500 border-t-transparent"></div>
                    <span className="text-xs text-gray-400">
                      {mfiProgressMessage || 'MFI 기술적 분석 중...'}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Sentiment Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      sentimentLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(sentimentLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      sentimentLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {sentimentLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(sentimentLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    sentimentLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Sentiment
                    <span className="ml-2 text-xs text-purple-400 font-normal">
                      뉴스분석
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  sentimentLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  sentimentLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  sentimentLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {sentimentLight === 'inactive' ? '대기중' : getStatusText(sentimentLight)}
                </span>
              </div>
            </div>

            {/* Volume Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      volumeLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(volumeLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      volumeLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {volumeLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(volumeLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    volumeLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Volume
                    <span className="ml-2 text-xs text-cyan-400 font-normal">
                      거래량
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  volumeLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  volumeLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  volumeLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {volumeLight === 'inactive' ? '대기중' : getStatusText(volumeLight)}
                </span>
              </div>
            </div>

            {/* Risk Light (Placeholder) */}
            <div>
              <div className="flex items-center justify-between group">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className={`w-6 h-6 rounded-full ${
                      riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight)
                    } shadow-lg transition-all duration-700 ease-in-out ${
                      riskLight === 'inactive' ? '' : 'animate-pulse'
                    }`}></div>
                    {riskLight !== 'inactive' && (
                      <div className={`absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(riskLight)} opacity-20 animate-ping`}></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium ${
                    riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'
                  } transition-colors`}>
                    Risk
                    <span className="ml-2 text-xs text-red-400 font-normal">
                      위험도
                    </span>
                  </span>
                </div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium transition-all duration-200 ${
                  riskLight === 'inactive' ? 'bg-gray-500/20 text-gray-400 border border-gray-500/30' :
                  riskLight === 'good' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  riskLight === 'warning' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)}
                </span>
              </div>
            </div>

            {/* Korean Timeout Message */}
            {showLongTaskMessage && (
              <div className="mt-4 max-w-full">
                <div className="text-xs text-yellow-400 bg-yellow-500/10 border border-yellow-500/20 rounded px-3 py-2 break-words text-center">
                  이 작업은 시간이 걸립니다... (최대 2분)
                </div>
              </div>
            )}
          </div>
        </div>

        {/* LSTM 상태 및 정보 표시 */}
        {lstmError && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-3">
            <div className="flex items-center space-x-2 text-xs text-red-400">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span>LSTM 오류: {lstmError}</span>
            </div>
          </div>
        )}

        {/* Debug State Display */}
        <div className="bg-slate-100 rounded-lg p-3 text-xs">
          <div className="font-semibold text-slate-700 mb-2">Debug State:</div>
          <div className="space-y-1 text-slate-600">
            <div>Forecast Light: <span className="font-mono">{forecastLight}</span></div>
            <div>Technical Light: <span className="font-mono">{technicalLight}</span></div>
            <div>LSTM Loading: <span className="font-mono">{lstmLoading.toString()}</span></div>
            <div>MFI Loading: <span className="font-mono">{mfiLoading.toString()}</span></div>
            <div>In Flight: <span className="font-mono">{inFlight.current.toString()}</span></div>
          </div>
          <button
            onClick={testTrafficLights}
            className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            Test Traffic Lights
          </button>
        </div>

        {/* 업데이트 정보 */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
            <svg className={`w-3 h-3 ${(lstmLoading || mfiLoading) ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>
              {(lstmLoading || mfiLoading) ? 'Intel 최적화 5-Light 분석 중...' : 'Intel 최적화 5-Light 종합 분석 시스템'}
            </span>
          </div>
          <div className="mt-1 text-xs text-green-400">
            예측 | 기술적 | 감정 | 거래량 | 위험도 - Intel oneDNN 가속
          </div>
        </div>
      </div>
    );
  }

  // 기본 모드 (시장 지표)
  return (
    <div className="space-y-4">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-slate-900">시장 현황</h3>
        <div className={`w-2 h-2 rounded-full animate-pulse ${loading ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
      </div>

      {/* 지표 목록 */}
      <div className="space-y-3">
        {loading && indicators.length === 0 ? (
          // 로딩 스켈레톤
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse">
              <div className="flex items-center justify-between mb-1">
                <div className="h-4 bg-slate-200 rounded w-16"></div>
                <div className="w-4 h-4 bg-slate-200 rounded"></div>
              </div>
              <div className="flex items-center justify-between">
                <div className="h-6 bg-slate-200 rounded w-20"></div>
                <div className="h-4 bg-slate-200 rounded w-12"></div>
              </div>
            </div>
          ))
        ) : (
          indicators.map((indicator, index) => (
          <div
            key={index}
            className="p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200"
          >
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-slate-700">{indicator.label}</span>
              {getTrendIcon(indicator.trend)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold text-slate-900">{indicator.value}</span>
              <span className={`text-sm font-medium ${getChangeColor(indicator.trend)}`}>
                {indicator.change}
              </span>
            </div>
          </div>
          ))
        )}
      </div>

      {/* 푸터 */}
      <div className="pt-3 border-t border-slate-200">
        <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>
            {loading ? '업데이트 중...' :
             lastUpdate ? `마지막 업데이트: ${lastUpdate}` :
             '20초마다 업데이트'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SpeedTrafficLegacy;
