/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3Nvbmc3JTVDJTVDRGVza3RvcCU1QyU1Q2hvbWUlNUMlNUN1YnVudHUlNUMlNUNmaW5hbmNpYWxfZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29uZzdcXFxcRGVza3RvcFxcXFxob21lXFxcXHVidW50dVxcXFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3Nvbmc3JTVDJTVDRGVza3RvcCU1QyU1Q2hvbWUlNUMlNUN1YnVudHUlNUMlNUNmaW5hbmNpYWxfZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29uZzdcXFxcRGVza3RvcFxcXFxob21lXFxcXHVidW50dVxcXFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csong7%5C%5CDesktop%5C%5Chome%5C%5Cubuntu%5C%5Cfinancial_dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_FinancialChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/FinancialChart */ \"(ssr)/./src/components/FinancialChart.tsx\");\n/* harmony import */ var _components_AIChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AIChat */ \"(ssr)/./src/components/AIChat.tsx\");\n/* harmony import */ var _components_SpeedTraffic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SpeedTraffic */ \"(ssr)/./src/components/SpeedTraffic.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    const [currentSymbol, setCurrentSymbol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [showingCompanyList, setShowingCompanyList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChartExpanded, setIsChartExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // 차트 확장 상태\n    const aiChatRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle phase messages from SpeedTraffic\n    const handlePhaseMessage = (message)=>{\n        aiChatRef.current?.addBotMessage(message);\n    };\n    // 홈으로 돌아가기 (새로고침 효과)\n    const handleHomeClick = ()=>{\n        console.log('🏠 Home button clicked - resetting all states');\n        setCurrentSymbol(undefined);\n        setShowingCompanyList(false);\n        setIsChartExpanded(false); // 차트 확장 상태 초기화\n        console.log('🔄 Triggering page reload for complete reset');\n        // 페이지 새로고침으로 모든 상태 초기화 (채팅 포함)\n        window.location.reload();\n    };\n    // 차트 확장/축소 토글 (채팅 상태 보존)\n    const handleToggleChartExpand = ()=>{\n        console.log('🔄 Toggling chart expand state:', {\n            current: isChartExpanded,\n            willBe: !isChartExpanded\n        });\n        setIsChartExpanded(!isChartExpanded);\n        console.log('✅ Chart expand toggle completed');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-screen overflow-hidden bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"relative bg-white border-b border-slate-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 gradient-bg opacity-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative px-4 sm:px-6 py-3 sm:py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"flex items-center space-x-2 sm:space-x-3 hover:bg-slate-50 rounded-lg p-2 -m-2 transition-colors duration-200 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-7 h-7 sm:w-8 sm:h-8 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-200 shadow-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/hanyang-logo.png\",\n                                                alt: \"한양대학교 로고\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-lg sm:text-xl font-semibold text-slate-900 group-hover:text-blue-600 transition-colors duration-200\",\n                                                    children: \"금융인공지능실무 AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs sm:text-sm text-slate-500 hidden sm:block group-hover:text-slate-600 transition-colors duration-200\",\n                                                    children: \"사용자 맞춤형 투자지원 AI - 2021064802/송승은\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm text-slate-600\",\n                                            children: \"실시간\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-1 overflow-hidden p-2 sm:p-4 gap-2 sm:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex flex-col flex-1 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${isChartExpanded ? 'fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4' // 확장 시 전체 화면 + 배경\n                                 : showingCompanyList ? 'h-16 flex-shrink-0' // 차트 바 고정 크기\n                                 : currentSymbol ? 'h-64 sm:h-80 flex-shrink-0' // 차트 표시 시 고정 높이로 더 작게\n                                 : 'h-60 sm:h-80 flex-shrink-0' // 기본 차트 크기\n                                } ${isChartExpanded ? '' : 'mb-2 sm:mb-3'} transition-all duration-600 ease-in-out animate-fade-in overflow-hidden`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${isChartExpanded ? 'w-full h-full max-w-7xl max-h-full' : 'h-full'} bg-white ${isChartExpanded ? 'rounded-lg' : 'rounded-lg sm:rounded-xl'} shadow-sm border border-slate-200 overflow-hidden`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FinancialChart__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        symbol: currentSymbol,\n                                        isMinimized: showingCompanyList,\n                                        isExpanded: isChartExpanded,\n                                        onToggleExpand: handleToggleChartExpand\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${isChartExpanded ? 'hidden' // 확장 시 숨김 (display: none으로 DOM은 유지하되 렌더링 안함)\n                                 : showingCompanyList ? 'flex-1' // 기업 리스트 표시 시 전체 공간 사용\n                                 : currentSymbol ? 'flex-1' // 차트 있을 때 나머지 공간 모두 사용\n                                 : 'h-96 sm:h-[32rem]' // 기본 채팅창 크기 증가\n                                } min-h-0 transition-all duration-600 ease-out animate-slide-up`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-white rounded-lg sm:rounded-xl shadow-sm border border-slate-200 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        ref: aiChatRef,\n                                        onSymbolSubmit: (s)=>{\n                                            console.log('📊 Symbol submitted from chat:', s);\n                                            setCurrentSymbol(s);\n                                            setShowingCompanyList(false);\n                                        },\n                                        onSymbolError: ()=>{\n                                            console.log('❌ Symbol error from chat');\n                                            setCurrentSymbol(undefined);\n                                            setShowingCompanyList(false);\n                                        },\n                                        onShowingCompanyList: (showing)=>{\n                                            console.log('📋 Company list visibility changed:', showing);\n                                            setShowingCompanyList(showing);\n                                        },\n                                        hasChart: !!currentSymbol,\n                                        showingCompanyList: showingCompanyList,\n                                        isChartExpanded: isChartExpanded\n                                    }, \"persistent-chat\" // 고정 key로 컴포넌트 상태 보존\n                                    , false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"hidden lg:block w-80 animate-fade-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full bg-white rounded-xl shadow-sm border border-slate-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-slate-900 mb-1\",\n                                            children: currentSymbol ? '투자 분석' : '시장 현황'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-500\",\n                                            children: currentSymbol ? 'AI 기반 투자 적격성 분석' : '실시간 시장 지표'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SpeedTraffic__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    symbol: currentSymbol,\n                                    onPhaseMessage: handlePhaseMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden fixed bottom-4 right-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105\",\n                            onClick: ()=>{},\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AIChat.tsx":
/*!***********************************!*\
  !*** ./src/components/AIChat.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AIChat = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ onSymbolSubmit, onSymbolError, onShowingCompanyList, hasChart, showingCompanyList, isChartExpanded }, ref)=>{\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMoreButton, setShowMoreButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoadingMore, setIsLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [suggestedQuestions, setSuggestedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHidingSuggestions, setIsHidingSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollDiv = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastMessageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 성능 최적화된 질문 예시 생성 (메모이제이션)\n    const QUESTION_POOLS = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AIChat.useMemo[QUESTION_POOLS]\": ()=>({\n                casual: [\n                    '넌 누구니?',\n                    '잘할 수 있어?',\n                    '뭐하고 있어?',\n                    '안녕하세요',\n                    '고마워',\n                    '넌 뭐야?',\n                    '넌 몇 살이야?'\n                ],\n                industry: [\n                    '반도체 산업',\n                    '자동차 관련 기업',\n                    '바이오테크놀로지',\n                    '은행 금융 기업',\n                    '미디어 엔터테인먼트',\n                    '소프트웨어 회사들',\n                    '클라우드 IT 서비스',\n                    '의료기기 회사',\n                    '제약회사들',\n                    '항공우주 방위산업',\n                    '투자 추천해줘',\n                    '어떤 기업이 좋을까?'\n                ],\n                company: [\n                    '테슬라',\n                    '애플',\n                    '마이크로소프트',\n                    '인텔',\n                    '엔비디아',\n                    '구글',\n                    '아마존',\n                    '메타',\n                    'AMD',\n                    '퀄컴'\n                ]\n            })\n    }[\"AIChat.useMemo[QUESTION_POOLS]\"], []);\n    const generateSuggestedQuestions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[generateSuggestedQuestions]\": ()=>{\n            const getRandomItem = {\n                \"AIChat.useCallback[generateSuggestedQuestions].getRandomItem\": (arr)=>arr[Math.floor(Math.random() * arr.length)]\n            }[\"AIChat.useCallback[generateSuggestedQuestions].getRandomItem\"];\n            const casualQ = getRandomItem(QUESTION_POOLS.casual);\n            const industryQ1 = getRandomItem(QUESTION_POOLS.industry);\n            let industryQ2 = getRandomItem(QUESTION_POOLS.industry);\n            while(industryQ2 === industryQ1){\n                industryQ2 = getRandomItem(QUESTION_POOLS.industry);\n            }\n            const companyQ = getRandomItem(QUESTION_POOLS.company);\n            return [\n                casualQ,\n                industryQ1,\n                industryQ2,\n                companyQ\n            ];\n        }\n    }[\"AIChat.useCallback[generateSuggestedQuestions]\"], [\n        QUESTION_POOLS\n    ]);\n    // 컴포넌트 마운트 시 질문 예시 생성\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const questions = generateSuggestedQuestions();\n            console.log('Generated suggested questions:', questions);\n            setSuggestedQuestions(questions);\n        }\n    }[\"AIChat.useEffect\"], [\n        generateSuggestedQuestions\n    ]);\n    /* 자동 스크롤 - history 변경 시와 차트 상태 변경 시 */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const scrollToBottom = {\n                \"AIChat.useEffect.scrollToBottom\": ()=>{\n                    // 마지막 메시지로 스크롤 (더 정확함)\n                    if (lastMessageRef.current) {\n                        lastMessageRef.current.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'end',\n                            inline: 'nearest'\n                        });\n                    } else if (scrollDiv.current) {\n                        // fallback: 컨테이너 맨 아래로 스크롤\n                        scrollDiv.current.scrollTo({\n                            top: scrollDiv.current.scrollHeight,\n                            behavior: 'smooth'\n                        });\n                    }\n                }\n            }[\"AIChat.useEffect.scrollToBottom\"];\n            // 약간의 지연을 두고 스크롤 (DOM 업데이트 완료 후)\n            const timer = setTimeout(scrollToBottom, 100);\n            return ({\n                \"AIChat.useEffect\": ()=>clearTimeout(timer)\n            })[\"AIChat.useEffect\"];\n        }\n    }[\"AIChat.useEffect\"], [\n        history,\n        hasChart\n    ]);\n    /* 컨테이너 크기 변경 감지 및 스크롤 재조정 */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            const scrollContainer = scrollDiv.current;\n            if (!scrollContainer) return;\n            const resizeObserver = new ResizeObserver({\n                \"AIChat.useEffect\": ()=>{\n                    // 크기 변경 후 스크롤을 맨 아래로\n                    setTimeout({\n                        \"AIChat.useEffect\": ()=>{\n                            if (scrollContainer) {\n                                scrollContainer.scrollTo({\n                                    top: scrollContainer.scrollHeight,\n                                    behavior: 'smooth'\n                                });\n                            }\n                        }\n                    }[\"AIChat.useEffect\"], 50);\n                }\n            }[\"AIChat.useEffect\"]);\n            resizeObserver.observe(scrollContainer);\n            return ({\n                \"AIChat.useEffect\": ()=>{\n                    resizeObserver.disconnect();\n                }\n            })[\"AIChat.useEffect\"];\n        }\n    }[\"AIChat.useEffect\"], []);\n    // 컴포넌트 마운트 시 환영 메시지 가져오기 (차트 상태 변경 시 초기화 방지)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIChat.useEffect\": ()=>{\n            // 이미 채팅 히스토리가 있으면 환영 메시지를 다시 로드하지 않음 (상태 보존)\n            if (history.length > 0) {\n                console.log('💬 Chat history exists, skipping welcome message reload');\n                return;\n            }\n            const getWelcomeMessage = {\n                \"AIChat.useEffect.getWelcomeMessage\": async ()=>{\n                    console.log('👋 Loading welcome message');\n                    try {\n                        const res = await send({\n                            message: ''\n                        }); // 빈 메시지로 환영 메시지 요청\n                        setHistory([\n                            {\n                                from: 'bot',\n                                text: res.reply\n                            }\n                        ]);\n                        console.log('✅ Welcome message loaded successfully');\n                    } catch (error) {\n                        console.error('❌ Failed to get welcome message:', error);\n                        // 실패 시 기본 환영 메시지\n                        setHistory([\n                            {\n                                from: 'bot',\n                                text: '안녕하세요! 원하시는 소재나 산업 등을 자유롭게 말씀하시면 알맞는 산업을 찾아드리겠습니다.'\n                            }\n                        ]);\n                        console.log('🔄 Fallback welcome message set');\n                    }\n                }\n            }[\"AIChat.useEffect.getWelcomeMessage\"];\n            getWelcomeMessage();\n        }\n    }[\"AIChat.useEffect\"], []); // 마운트 시에만 실행 (history 의존성 제거로 상태 보존)\n    // 성능 최적화된 API 호출 (메모이제이션)\n    const send = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[send]\": async (body)=>{\n            try {\n                const res = await fetch('/api/ai_chat', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(body)\n                });\n                if (!res.ok) {\n                    throw new Error(`HTTP error! status: ${res.status}`);\n                }\n                return await res.json();\n            } catch (error) {\n                console.error('API call failed:', error);\n                throw error;\n            }\n        }\n    }[\"AIChat.useCallback[send]\"], []);\n    // Expose methods to parent component\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"AIChat.useImperativeHandle\": ()=>({\n                addBotMessage: ({\n                    \"AIChat.useImperativeHandle\": (message)=>{\n                        setHistory({\n                            \"AIChat.useImperativeHandle\": (h)=>[\n                                    ...h,\n                                    {\n                                        from: 'bot',\n                                        text: message\n                                    }\n                                ]\n                        }[\"AIChat.useImperativeHandle\"]);\n                        // Auto-scroll to bottom\n                        setTimeout({\n                            \"AIChat.useImperativeHandle\": ()=>{\n                                scrollDiv.current?.scrollTo({\n                                    top: scrollDiv.current.scrollHeight,\n                                    behavior: 'smooth'\n                                });\n                            }\n                        }[\"AIChat.useImperativeHandle\"], 100);\n                    }\n                })[\"AIChat.useImperativeHandle\"]\n            })\n    }[\"AIChat.useImperativeHandle\"], []);\n    // 성능 최적화된 패턴 감지 (메모이제이션)\n    const DETECTION_PATTERNS = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AIChat.useMemo[DETECTION_PATTERNS]\": ()=>({\n                companyList: [\n                    /산업의?\\s*(주요\\s*)?기업들?입니다/,\n                    /분야의?\\s*(대표\\s*)?기업들?입니다/,\n                    /산업에는?\\s*다음과?\\s*같은\\s*기업들?이\\s*있습니다/,\n                    /\\d+\\.\\s*[가-힣A-Za-z\\s]+\\s*\\([A-Z]+\\)/,\n                    /등이\\s*있습니다/,\n                    /관심\\s*있는\\s*기업이\\s*있나요/,\n                    /어떤\\s*회사가\\s*궁금하신가요/\n                ],\n                moreButton: [\n                    /더 많은 기업을 보시려면.*더보기.*말씀해 주세요/,\n                    /총 \\d+개 기업/\n                ]\n            })\n    }[\"AIChat.useMemo[DETECTION_PATTERNS]\"], []);\n    const detectCompanyList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[detectCompanyList]\": (text)=>DETECTION_PATTERNS.companyList.some({\n                \"AIChat.useCallback[detectCompanyList]\": (pattern)=>pattern.test(text)\n            }[\"AIChat.useCallback[detectCompanyList]\"])\n    }[\"AIChat.useCallback[detectCompanyList]\"], [\n        DETECTION_PATTERNS\n    ]);\n    const detectMoreButton = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[detectMoreButton]\": (text)=>DETECTION_PATTERNS.moreButton.some({\n                \"AIChat.useCallback[detectMoreButton]\": (pattern)=>pattern.test(text)\n            }[\"AIChat.useCallback[detectMoreButton]\"])\n    }[\"AIChat.useCallback[detectMoreButton]\"], [\n        DETECTION_PATTERNS\n    ]);\n    // 공통 응답 처리 로직 (메모이제이션)\n    const handleApiResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIChat.useCallback[handleApiResponse]\": (res)=>{\n            const isShowingCompanies = res.status === 'showing_companies' || detectCompanyList(res.reply);\n            const shouldShowMoreButton = res.hasMore || detectMoreButton(res.reply);\n            if (isShowingCompanies) {\n                onShowingCompanyList?.(true);\n            }\n            setShowMoreButton(shouldShowMoreButton);\n            // 차트 요청 처리\n            if (res.status === 'chart_requested' && res.symbol) {\n                onSymbolSubmit?.(res.symbol);\n                onShowingCompanyList?.(false);\n                setShowMoreButton(false);\n                // 스크롤 재조정\n                setTimeout({\n                    \"AIChat.useCallback[handleApiResponse]\": ()=>{\n                        scrollDiv.current?.scrollTo({\n                            top: scrollDiv.current.scrollHeight,\n                            behavior: 'smooth'\n                        });\n                    }\n                }[\"AIChat.useCallback[handleApiResponse]\"], 200);\n                // 세션 리셋\n                setTimeout({\n                    \"AIChat.useCallback[handleApiResponse]\": async ()=>{\n                        try {\n                            await send({\n                                message: '__RESET_SESSION__'\n                            });\n                        } catch (error) {\n                            console.error('Failed to reset session:', error);\n                        }\n                    }\n                }[\"AIChat.useCallback[handleApiResponse]\"], 1000);\n            } else if (res.status === 'error') {\n                onSymbolError?.();\n                onShowingCompanyList?.(false);\n                setShowMoreButton(false);\n            }\n        }\n    }[\"AIChat.useCallback[handleApiResponse]\"], [\n        detectCompanyList,\n        detectMoreButton,\n        onShowingCompanyList,\n        onSymbolSubmit,\n        onSymbolError,\n        send\n    ]);\n    // 최적화된 질문 예시 버튼 클릭 핸들러\n    const handleSuggestedQuestionClick = async (question)=>{\n        // 질문 예시 버튼 부드럽게 숨기기\n        setIsHidingSuggestions(true);\n        setTimeout(()=>{\n            setSuggestedQuestions([]);\n            setIsHidingSuggestions(false);\n        }, 300);\n        // 사용자 메시지로 추가\n        setHistory((h)=>[\n                ...h,\n                {\n                    from: 'user',\n                    text: question\n                }\n            ]);\n        try {\n            const res = await send({\n                message: question,\n                history\n            });\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: res.reply\n                    }\n                ]);\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('Suggested question error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 일시적인 오류가 발생했습니다.'\n                    }\n                ]);\n            onSymbolError?.();\n            onShowingCompanyList?.(false);\n            setShowMoreButton(false);\n        }\n    };\n    // 최적화된 더보기 버튼 클릭 핸들러\n    const handleMoreClick = async ()=>{\n        setIsLoadingMore(true);\n        setShowMoreButton(false);\n        try {\n            const res = await send({\n                message: '더보기',\n                history\n            });\n            // 마지막 봇 메시지를 새로운 전체 리스트로 대체\n            setHistory((h)=>{\n                const newHistory = [\n                    ...h\n                ];\n                for(let i = newHistory.length - 1; i >= 0; i--){\n                    if (newHistory[i].from === 'bot') {\n                        newHistory[i] = {\n                            from: 'bot',\n                            text: res.reply\n                        };\n                        break;\n                    }\n                }\n                return newHistory;\n            });\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('More companies error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 더보기 요청 중 오류가 발생했습니다.'\n                    }\n                ]);\n        } finally{\n            setIsLoadingMore(false);\n        }\n    };\n    // 최적화된 메인 제출 핸들러\n    const onSubmit = async (e)=>{\n        e.preventDefault();\n        const text = inputRef.current?.value.trim();\n        if (!text) return;\n        setHistory((h)=>[\n                ...h,\n                {\n                    from: 'user',\n                    text\n                }\n            ]);\n        inputRef.current.value = '';\n        // 질문 예시 버튼 숨기기\n        if (suggestedQuestions.length > 0) {\n            setIsHidingSuggestions(true);\n            setTimeout(()=>{\n                setSuggestedQuestions([]);\n                setIsHidingSuggestions(false);\n            }, 300);\n        }\n        try {\n            const res = await send({\n                message: text,\n                history\n            });\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: res.reply\n                    }\n                ]);\n            handleApiResponse(res);\n        } catch (error) {\n            console.error('Chat error:', error);\n            setHistory((h)=>[\n                    ...h,\n                    {\n                        from: 'bot',\n                        text: '죄송합니다. 일시적인 오류가 발생했습니다.'\n                    }\n                ]);\n            onSymbolError?.();\n            onShowingCompanyList?.(false);\n            setShowMoreButton(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full max-h-full relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 sm:px-4 py-2 border-b border-slate-100 bg-slate-50/50 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold text-slate-900\",\n                                children: \"AI 어시스턴트\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollDiv,\n                className: \"flex-1 min-h-0 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4 scroll-smooth bg-gradient-to-b from-slate-50/30 to-white\",\n                style: {\n                    scrollBehavior: 'smooth',\n                    transition: 'all 700ms cubic-bezier(0.4, 0, 0.2, 1)',\n                    maxHeight: showingCompanyList ? 'calc(100vh - 200px)' // 질문 블럭 공간 고려\n                     : hasChart ? '180px' : '280px',\n                    height: showingCompanyList ? 'calc(100vh - 200px)' : hasChart ? '180px' : '280px'\n                },\n                children: [\n                    history.map((m, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: i === history.length - 1 ? lastMessageRef : null,\n                            className: `flex ${m.from === 'user' ? 'justify-end' : 'justify-start'} animate-slide-up`,\n                            children: m.from === 'user' ? // 사용자 메시지 (오른쪽 정렬)\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start flex-row-reverse max-w-[85%] sm:max-w-[80%]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm bg-gradient-to-br from-blue-500 to-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 rounded-xl shadow-sm whitespace-pre-line bg-gradient-to-br from-blue-500 to-blue-600 text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm leading-relaxed\",\n                                            children: m.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, undefined) : // AI 메시지 (왼쪽 정렬)\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start max-w-[85%] sm:max-w-[80%]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm bg-gradient-to-br from-slate-100 to-slate-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-slate-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 rounded-xl shadow-sm whitespace-pre-line bg-white border border-slate-200 text-slate-900\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm leading-relaxed\",\n                                            children: m.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, undefined)),\n                    showMoreButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleMoreClick,\n                            disabled: isLoadingMore,\n                            className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center space-x-2\",\n                            children: isLoadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"로딩중...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M19 9l-7 7-7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"더보기\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            suggestedQuestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-3 py-3 bg-slate-50/50 border-t border-slate-100 flex-shrink-0 transition-all duration-700 ease-out ${isHidingSuggestions ? 'opacity-0 transform translate-y-2' : 'opacity-100 transform translate-y-0 animate-slide-up'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-slate-500 mb-3 text-center\",\n                        children: \"\\uD83D\\uDCA1 이런 질문은 어떠세요?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 justify-center\",\n                        children: suggestedQuestions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSuggestedQuestionClick(question),\n                                className: `px-3 py-1.5 bg-white hover:bg-slate-50 text-slate-700 text-xs rounded-lg transition-all duration-200 border border-slate-200 shadow-sm ${isHidingSuggestions ? 'opacity-0' : 'opacity-100'}`,\n                                style: {\n                                    animationDelay: isHidingSuggestions ? '0ms' : `${index * 100}ms`\n                                },\n                                children: question\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 420,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 sm:p-3 border-t border-slate-100 bg-white flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: onSubmit,\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                placeholder: \"메시지를 입력하세요...\",\n                                className: \"w-full input-modern pr-9 text-sm py-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"absolute right-1 top-1/2 transform -translate-y-1/2 w-7 h-7 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center justify-center transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3 h-3\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\AIChat.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, undefined);\n});\nAIChat.displayName = 'AIChat';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIChat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIChat.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FinancialChart.tsx":
/*!*******************************************!*\
  !*** ./src/components/FinancialChart.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lightweight_charts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lightweight-charts */ \"(ssr)/./node_modules/lightweight-charts/dist/lightweight-charts.development.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FinancialChart = ({ symbol, isMinimized, isExpanded, onToggleExpand })=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const seriesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // ESC 키로 확장 모드 종료\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"FinancialChart.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && isExpanded) {\n                        onToggleExpand?.();\n                    }\n                }\n            }[\"FinancialChart.useEffect.handleKeyDown\"];\n            if (isExpanded) {\n                document.addEventListener('keydown', handleKeyDown);\n                return ({\n                    \"FinancialChart.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n                })[\"FinancialChart.useEffect\"];\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        isExpanded,\n        onToggleExpand\n    ]);\n    // 확장 상태 변경 시 차트 크기만 조정 (재생성 방지로 채팅 상태 보존)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('🔄 Chart expand state changed:', {\n                isExpanded,\n                hasChart: !!chartRef.current\n            });\n            if (!chartRef.current) {\n                console.log('⚠️ No chart instance found, skipping resize');\n                return;\n            }\n            try {\n                // 확장 상태에 따른 새로운 높이 계산\n                const newHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                console.log('📏 Resizing chart to height:', newHeight);\n                // 차트 크기만 변경 (재생성하지 않음으로써 채팅 상태 보존)\n                chartRef.current.applyOptions({\n                    height: newHeight\n                });\n                // 차트 내용을 새로운 크기에 맞게 조정\n                chartRef.current.timeScale().fitContent();\n                console.log('✅ Chart resize completed successfully');\n            } catch (error) {\n                console.error('❌ Chart resize failed:', error);\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        isExpanded\n    ]);\n    // 차트 초기화 - symbol이 있을 때만 생성\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('🎯 Chart initialization effect triggered:', {\n                symbol,\n                hasChart: !!chartRef.current\n            });\n            if (!ref.current || !symbol) {\n                console.log('🧹 Cleaning up chart (no symbol or ref)');\n                // symbol이 없으면 기존 차트 제거\n                if (chartRef.current) {\n                    chartRef.current.remove();\n                    chartRef.current = null;\n                    seriesRef.current = null;\n                    console.log('✅ Chart cleanup completed');\n                }\n                return;\n            }\n            // 이미 차트가 있다면 스킵 (중복 생성 방지)\n            if (chartRef.current) {\n                console.log('⏭️ Chart already exists, skipping initialization');\n                return;\n            }\n            console.log('🏗️ Creating new chart instance');\n            try {\n                // 초기 차트 높이 설정\n                const initialHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                console.log('📐 Initial chart height:', initialHeight);\n                // 새로운 차트 생성\n                chartRef.current = (0,lightweight_charts__WEBPACK_IMPORTED_MODULE_2__.createChart)(ref.current, {\n                    height: initialHeight,\n                    layout: {\n                        background: {\n                            color: '#ffffff'\n                        },\n                        textColor: '#333'\n                    },\n                    grid: {\n                        vertLines: {\n                            color: '#f0f0f0'\n                        },\n                        horzLines: {\n                            color: '#f0f0f0'\n                        }\n                    },\n                    timeScale: {\n                        borderColor: '#cccccc',\n                        timeVisible: true,\n                        secondsVisible: false\n                    },\n                    rightPriceScale: {\n                        borderColor: '#cccccc',\n                        scaleMargins: {\n                            top: 0.1,\n                            bottom: 0.1\n                        }\n                    },\n                    watermark: {\n                        visible: false\n                    },\n                    crosshair: {\n                        mode: 1\n                    },\n                    handleScroll: {\n                        mouseWheel: true,\n                        pressedMouseMove: true\n                    },\n                    handleScale: {\n                        axisPressedMouseMove: true,\n                        mouseWheel: true,\n                        pinch: true\n                    }\n                });\n                // 라인 시리즈 추가\n                seriesRef.current = chartRef.current.addLineSeries({\n                    color: '#2563eb',\n                    lineWidth: 2,\n                    priceFormat: {\n                        type: 'price',\n                        precision: 2,\n                        minMove: 0.01\n                    },\n                    crosshairMarkerVisible: true,\n                    crosshairMarkerRadius: 4,\n                    lastValueVisible: true,\n                    priceLineVisible: true\n                });\n                console.log('✅ Chart instance created successfully');\n                // 윈도우 리사이즈 이벤트 리스너 추가\n                const handleResize = {\n                    \"FinancialChart.useEffect.handleResize\": ()=>{\n                        console.log('🔄 Window resize detected');\n                        if (chartRef.current) {\n                            const newHeight = isExpanded ? Math.max(window.innerHeight - 150, 400) : 400;\n                            console.log('📏 Applying new height on resize:', newHeight);\n                            chartRef.current.applyOptions({\n                                height: newHeight\n                            });\n                            chartRef.current.timeScale().fitContent();\n                        }\n                    }\n                }[\"FinancialChart.useEffect.handleResize\"];\n                window.addEventListener('resize', handleResize);\n                return ({\n                    \"FinancialChart.useEffect\": ()=>{\n                        console.log('🧹 Cleaning up resize listener');\n                        window.removeEventListener('resize', handleResize);\n                    }\n                })[\"FinancialChart.useEffect\"];\n            } catch (error) {\n                console.error('❌ Chart initialization failed:', error);\n            }\n        }\n    }[\"FinancialChart.useEffect\"], [\n        symbol\n    ]);\n    // 심볼이 변경될 때 데이터 로드\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialChart.useEffect\": ()=>{\n            console.log('📊 Data loading effect triggered:', {\n                symbol,\n                hasChart: !!chartRef.current,\n                hasSeries: !!seriesRef.current\n            });\n            if (!symbol || !seriesRef.current || !chartRef.current) {\n                console.log('⏭️ Skipping data load - missing requirements');\n                return;\n            }\n            const loadChartData = {\n                \"FinancialChart.useEffect.loadChartData\": async ()=>{\n                    console.log('🔄 Starting chart data load for symbol:', symbol);\n                    setIsLoading(true);\n                    setError(null);\n                    try {\n                        const res = await fetch(`/api/csv_chart_data?symbol=${symbol}`);\n                        const data = await res.json();\n                        if (data.error) {\n                            console.error('❌ Chart data API error:', data.error);\n                            setError(data.error);\n                            return;\n                        }\n                        console.log('📈 Processing chart data, points count:', data.data?.length || 0);\n                        // 데이터를 lightweight-charts 형식으로 변환\n                        const chartData = data.data.map({\n                            \"FinancialChart.useEffect.loadChartData.chartData\": (point)=>({\n                                    time: point.time,\n                                    value: point.value\n                                })\n                        }[\"FinancialChart.useEffect.loadChartData.chartData\"]);\n                        // 시리즈에 데이터 설정\n                        seriesRef.current?.setData(chartData);\n                        // 차트를 데이터에 맞게 조정\n                        if (chartRef.current) {\n                            chartRef.current.timeScale().fitContent();\n                            console.log('✅ Chart data loaded and fitted successfully');\n                        }\n                    } catch (error) {\n                        console.error('❌ Failed to load chart data:', error);\n                        setError('차트 데이터를 불러오는 중 오류가 발생했습니다.');\n                    } finally{\n                        setIsLoading(false);\n                        console.log('🏁 Chart data loading completed');\n                    }\n                }\n            }[\"FinancialChart.useEffect.loadChartData\"];\n            // 약간의 지연을 두고 데이터 로드 (차트 초기화 완료 후)\n            console.log('⏰ Scheduling data load with 200ms delay');\n            const timer = setTimeout(loadChartData, 200);\n            return ({\n                \"FinancialChart.useEffect\": ()=>{\n                    console.log('🧹 Cleaning up data load timer');\n                    clearTimeout(timer);\n                }\n            })[\"FinancialChart.useEffect\"];\n        }\n    }[\"FinancialChart.useEffect\"], [\n        symbol\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full flex flex-col\",\n        children: symbol ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-2 border-b border-slate-100 bg-gradient-to-r from-slate-50 to-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-7 h-7 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-slate-900\",\n                                                    children: [\n                                                        symbol,\n                                                        \" 주가 차트 \",\n                                                        isExpanded && '(확장 모드)'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-500\",\n                                                    children: isExpanded ? 'ESC 키로 닫기' : '실시간 차트'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-amber-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-amber-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"로딩중\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"오류\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"실시간\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        symbol && onToggleExpand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onToggleExpand,\n                                            className: \"px-2 py-1 text-xs bg-slate-500 hover:bg-slate-600 text-white rounded-md transition-colors duration-200 flex items-center space-x-1\",\n                                            title: isExpanded ? \"차트 축소\" : \"차트 확장\",\n                                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"축소\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"확장\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 text-red-500\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-red-700\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, undefined),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-h-0 relative bg-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: ref,\n                            className: \"absolute inset-0 w-full h-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 15\n                        }, undefined),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-purple-500 rounded-full animate-spin mx-auto\",\n                                                style: {\n                                                    animationDirection: 'reverse',\n                                                    animationDuration: '1.5s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-slate-900 mb-2\",\n                                        children: \"차트 데이터 로딩 중\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-500\",\n                                        children: \"최신 시장 정보를 가져오는 중...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true) : isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center h-full bg-gradient-to-r from-slate-50 to-white px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-semibold text-slate-900\",\n                                children: \"차트 생성 대기중\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: '0.4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                lineNumber: 339,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n            lineNumber: 338,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-slate-50 to-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center p-12 max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-blue-500\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 1.5,\n                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-slate-900 mb-3\",\n                        children: \"분석 준비 완료\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600 mb-4 leading-relaxed\",\n                        children: \"아래 AI 어시스턴트를 통해 관심 있는 기업이나 산업을 검색해보세요.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-2 text-sm text-slate-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"실시간 시장 데이터 기반\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n                lineNumber: 357,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n            lineNumber: 356,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\FinancialChart.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(FinancialChart));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FinancialChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SpeedTraffic.tsx":
/*!*****************************************!*\
  !*** ./src/components/SpeedTraffic.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SpeedTraffic = ({ symbol, onPhaseMessage })=>{\n    // Market indicators state (Pre-ticker mode)\n    const [indicators, setIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Traffic lights state (Post-ticker mode) - New order\n    const [technicalLight, setTechnicalLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 1: Technical Analysis\n    const [industryLight, setIndustryLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 2: Industry Sensitivity\n    const [marketLight, setMarketLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 3: Market Sensitivity (CAPM)\n    const [riskLight, setRiskLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 4: Volatility Risk\n    const [neuralLight, setNeuralLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 5: Neural Network (LSTM)\n    // Prediction state\n    const [phase1Loading, setPhase1Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phase2Loading, setPhase2Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lstmLoading, setLstmLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [predictionError, setPredictionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTimeoutMessage, setShowTimeoutMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastRequestTime, setLastRequestTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Single-flight guard\n    const inFlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 실제 시장 데이터 가져오기 (Pre-ticker mode)\n    const fetchMarketData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/market_data');\n            const result = await response.json();\n            if (result.success && result.data) {\n                const formattedData = result.data.map((item)=>({\n                        label: item.label,\n                        value: item.price.toLocaleString('en-US', {\n                            minimumFractionDigits: 2,\n                            maximumFractionDigits: 2\n                        }),\n                        change: `${item.changePercent >= 0 ? '+' : ''}${item.changePercent.toFixed(2)}%`,\n                        trend: item.trend,\n                        color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'\n                    }));\n                setIndicators(formattedData);\n                setLastUpdate(new Date().toLocaleTimeString('ko-KR'));\n            }\n        } catch (error) {\n            console.error('Failed to fetch market data:', error);\n            // 에러 시 fallback 데이터 사용\n            setIndicators([\n                {\n                    label: 'S&P 500',\n                    value: '4,567.89',\n                    change: '+1.2%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '나스닥',\n                    value: '14,234.56',\n                    change: '+0.8%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '다우존스',\n                    value: '34,567.12',\n                    change: '-0.3%',\n                    trend: 'down',\n                    color: 'red'\n                },\n                {\n                    label: 'VIX',\n                    value: '18.45',\n                    change: '-2.1%',\n                    trend: 'down',\n                    color: 'yellow'\n                },\n                {\n                    label: '달러/원',\n                    value: '1,327.50',\n                    change: '+0.5%',\n                    trend: 'up',\n                    color: 'green'\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Convert result color to traffic light status\n    const resultColorToStatus = (color)=>{\n        switch(color?.toLowerCase()){\n            case 'green':\n            case 'good':\n                return 'good';\n            case 'yellow':\n            case 'warning':\n                return 'warning';\n            case 'red':\n            case 'danger':\n                return 'danger';\n            default:\n                return 'warning';\n        }\n    };\n    // Extract color from LSTM/MFI results\n    const getColorFromResult = (obj)=>{\n        if (!obj) return undefined;\n        if (typeof obj === 'string') return obj;\n        if (obj.result_color) return obj.result_color;\n        if (obj.traffic_light) return obj.traffic_light;\n        if (obj.color) return obj.color;\n        return undefined;\n    };\n    // Staged execution: Phase 1 (Fast services) + Phase 2 (LSTM)\n    const fetchStagedPrediction = async ()=>{\n        if (!symbol || inFlight.current) return;\n        // Prevent too frequent requests (minimum 10 seconds between requests)\n        const now = Date.now();\n        if (now - lastRequestTime < 10000) {\n            console.log('Prediction request throttled - too frequent');\n            return;\n        }\n        try {\n            // Set single-flight guard\n            inFlight.current = true;\n            setLastRequestTime(now);\n            // Reset all lights to inactive state\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n            setPredictionError(null);\n            setShowTimeoutMessage(false);\n            console.log(`[SpeedTraffic] Starting staged prediction for ${symbol}`);\n            // Phase 1: Execute fast services (Technical, Industry, Market, Volatility)\n            setPhase1Loading(true);\n            console.log(`[SpeedTraffic] Phase 1: Starting fast services for ${symbol}`);\n            const phase1Response = await fetch(`/api/speedtraffic_staged?symbol=${symbol}&stage=phase1`, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!phase1Response.ok) {\n                throw new Error(`Phase 1 HTTP ${phase1Response.status}: ${phase1Response.statusText}`);\n            }\n            const phase1Result = await phase1Response.json();\n            console.log(`[SpeedTraffic] Phase 1 result:`, phase1Result);\n            // Update lights 1-4 immediately after Phase 1 completes\n            if (phase1Result.traffic_lights) {\n                if (phase1Result.traffic_lights.technical) {\n                    setTechnicalLight(resultColorToStatus(phase1Result.traffic_lights.technical));\n                    console.log(`[SpeedTraffic] Technical light set to: ${phase1Result.traffic_lights.technical}`);\n                }\n                if (phase1Result.traffic_lights.industry) {\n                    setIndustryLight(resultColorToStatus(phase1Result.traffic_lights.industry));\n                    console.log(`[SpeedTraffic] Industry light set to: ${phase1Result.traffic_lights.industry}`);\n                }\n                if (phase1Result.traffic_lights.market) {\n                    setMarketLight(resultColorToStatus(phase1Result.traffic_lights.market));\n                    console.log(`[SpeedTraffic] Market light set to: ${phase1Result.traffic_lights.market}`);\n                }\n                if (phase1Result.traffic_lights.risk) {\n                    setRiskLight(resultColorToStatus(phase1Result.traffic_lights.risk));\n                    console.log(`[SpeedTraffic] Risk light set to: ${phase1Result.traffic_lights.risk}`);\n                }\n            }\n            setPhase1Loading(false);\n            console.log(`[SpeedTraffic] Phase 1 completed successfully for ${symbol}`);\n            // Send Phase 1 completion message to chat\n            onPhaseMessage?.('기술적 분석, 산업 민감도, 시장 민감도, 변동성 리스크 분석을 마쳤어요! 📊');\n            // Phase 2: Execute LSTM service\n            setPhase2Loading(true);\n            setLstmLoading(true);\n            console.log(`[SpeedTraffic] Phase 2: Starting LSTM service for ${symbol}`);\n            // Send Phase 2 entry message to chat\n            onPhaseMessage?.('이제 딥러닝 기반 가격 변동 예측을 진행합니다. 잠시만 기다려 주세요! 🤖');\n            // Start 20-second timer for Korean timeout message\n            const timeoutTimer = setTimeout(()=>{\n                setShowTimeoutMessage(true);\n            }, 20000);\n            const phase2Response = await fetch(`/api/speedtraffic_staged?symbol=${symbol}&stage=phase2`, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(60000)\n            });\n            clearTimeout(timeoutTimer);\n            if (!phase2Response.ok) {\n                throw new Error(`Phase 2 HTTP ${phase2Response.status}: ${phase2Response.statusText}`);\n            }\n            const phase2Result = await phase2Response.json();\n            console.log(`[SpeedTraffic] Phase 2 result:`, phase2Result);\n            // Update light 5 after Phase 2 completes\n            if (phase2Result.traffic_lights && phase2Result.traffic_lights.neural) {\n                setNeuralLight(resultColorToStatus(phase2Result.traffic_lights.neural));\n                console.log(`[SpeedTraffic] Neural light set to: ${phase2Result.traffic_lights.neural}`);\n            }\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            console.log(`[SpeedTraffic] Phase 2 completed successfully for ${symbol}`);\n            // Send Phase 2 completion message to chat\n            onPhaseMessage?.('모든 분석이 완료되었습니다! 결과를 확인해 보세요. ✨');\n            // REMOVED: Automatic fine-tuning data collection\n            // User prefers manual fine-tuning data collection via standalone script\n            console.log(`[SpeedTraffic] All phases completed successfully for ${symbol}`);\n        } catch (error) {\n            console.error('Staged prediction error:', error);\n            if (error instanceof Error) {\n                if (error.name === 'TimeoutError') {\n                    setPredictionError('요청 시간 초과');\n                } else {\n                    setPredictionError(`예측 실패: ${error.message}`);\n                }\n            } else {\n                setPredictionError('예측 서비스 연결 실패');\n            }\n            // Reset all lights to inactive on error\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n        } finally{\n            setPhase1Loading(false);\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            setShowTimeoutMessage(false);\n            inFlight.current = false;\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (symbol) {\n                // When symbol is provided, fetch staged prediction once\n                fetchStagedPrediction();\n            } else {\n                // When no symbol, fetch market data initially\n                fetchMarketData();\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (!symbol) {\n                const interval = setInterval({\n                    \"SpeedTraffic.useEffect.interval\": ()=>{\n                        fetchMarketData();\n                    }\n                }[\"SpeedTraffic.useEffect.interval\"], 20000);\n                return ({\n                    \"SpeedTraffic.useEffect\": ()=>clearInterval(interval)\n                })[\"SpeedTraffic.useEffect\"];\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // Utility functions for rendering\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case 'up':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M7 17l9.2-9.2M17 17V7H7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, undefined);\n            case 'down':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 7l-9.2 9.2M7 7v10h10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-slate-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 12H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const getChangeColor = (trend)=>{\n        switch(trend){\n            case 'up':\n                return 'text-green-600';\n            case 'down':\n                return 'text-red-600';\n            default:\n                return 'text-slate-500';\n        }\n    };\n    const getTrafficLightColor = (status)=>{\n        switch(status){\n            case 'good':\n                return 'bg-green-500';\n            case 'warning':\n                return 'bg-yellow-500';\n            case 'danger':\n                return 'bg-red-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'good':\n                return '양호';\n            case 'warning':\n                return '보통';\n            case 'danger':\n                return '주의';\n            default:\n                return '분석중';\n        }\n    };\n    // Post-ticker mode: Traffic lights display\n    if (symbol) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-6 shadow-xl border border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight)} shadow-lg transition-all duration-700 ease-in-out ${technicalLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        technicalLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(technicalLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"기술적 분석\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${technicalLight === 'inactive' ? 'text-gray-400' : technicalLight === 'good' ? 'text-green-300' : technicalLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${industryLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(industryLight)} shadow-lg transition-all duration-700 ease-in-out ${industryLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        industryLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(industryLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${industryLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"산업 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${industryLight === 'inactive' ? 'text-gray-400' : industryLight === 'good' ? 'text-green-300' : industryLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: industryLight === 'inactive' ? '대기중' : getStatusText(industryLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${marketLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(marketLight)} shadow-lg transition-all duration-700 ease-in-out ${marketLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        marketLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(marketLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${marketLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"시장 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${marketLight === 'inactive' ? 'text-gray-400' : marketLight === 'good' ? 'text-green-300' : marketLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: marketLight === 'inactive' ? '대기중' : getStatusText(marketLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight)} shadow-lg transition-all duration-700 ease-in-out ${riskLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        riskLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(riskLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: \"변동성 리스크\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${riskLight === 'inactive' ? 'text-gray-400' : riskLight === 'good' ? 'text-green-300' : riskLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-6 h-6 rounded-full ${neuralLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(neuralLight)} shadow-lg transition-all duration-700 ease-in-out ${neuralLight === 'inactive' ? '' : 'animate-pulse'}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        neuralLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `absolute inset-0 w-6 h-6 rounded-full ${getTrafficLightColor(neuralLight)} opacity-20 animate-ping`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        lstmLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full border-2 border-blue-500 border-t-transparent animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm font-medium ${neuralLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300'} transition-colors`,\n                                                    children: lstmLoading ? '딥러닝 기반 예측(LSTM)' : '신경망 예측(LSTM)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs px-2 py-1 font-medium whitespace-nowrap ${neuralLight === 'inactive' ? 'text-gray-400' : neuralLight === 'good' ? 'text-green-300' : neuralLight === 'warning' ? 'text-yellow-300' : 'text-red-300'}`,\n                                            children: lstmLoading ? '분석중...' : neuralLight === 'inactive' ? '대기중' : getStatusText(neuralLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, undefined),\n                phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"딥러닝 기반 예측(LSTM) 분석 중...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 11\n                }, undefined),\n                showTimeoutMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-600 text-center\",\n                        children: \"이 작업은 시간이 걸립니다... (최대 60초)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-red-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"오류: \",\n                                    predictionError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && !phase1Loading && !phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchStagedPrediction,\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors\",\n                        children: \"다시 시도\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n            lineNumber: 370,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pre-ticker mode: Market indicators display\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-slate-900\",\n                        children: \"시장 현황\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-2 h-2 rounded-full animate-pulse ${loading ? 'bg-yellow-400' : 'bg-green-400'}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: loading && indicators.length === 0 ? // 로딩 스켈레톤\n                Array.from({\n                    length: 5\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-slate-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-slate-200 rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 13\n                    }, undefined)) : indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-slate-700\",\n                                        children: indicator.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    getTrendIcon(indicator.trend)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-slate-900\",\n                                        children: indicator.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${getChangeColor(indicator.trend)}`,\n                                        children: indicator.change\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-3 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-2 text-xs text-slate-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: loading ? '업데이트 중...' : lastUpdate ? `마지막 업데이트: ${lastUpdate}` : '20초마다 업데이트'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n        lineNumber: 592,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpeedTraffic);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SpeedTraffic.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5c621e197dd9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbmc3XFxEZXNrdG9wXFxob21lXFx1YnVudHVcXGZpbmFuY2lhbF9kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVjNjIxZTE5N2RkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: '사용자 맞춤 투자지원 AI',\n    description: '한양대학교 금융인공지능실무 - 사용자 맞춤형 투자지원 AI 시스템',\n    icons: {\n        icon: '/hanyang-logo.png',\n        shortcut: '/hanyang-logo.png',\n        apple: '/hanyang-logo.png'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ko\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\home\\ubuntu\\financial_dashboard\\src\\app\\page.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/fancy-canvas","vendor-chunks/lightweight-charts"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csong7%5CDesktop%5Chome%5Cubuntu%5Cfinancial_dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();