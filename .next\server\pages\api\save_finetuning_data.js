"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/save_finetuning_data";
exports.ids = ["pages/api/save_finetuning_data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_finetuning_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_finetuning_data.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_finetuning_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_finetuning_data.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_save_finetuning_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\save_finetuning_data.ts */ \"(api)/./src/pages/api/save_finetuning_data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_save_finetuning_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_save_finetuning_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/save_finetuning_data\",\n        pathname: \"/api/save_finetuning_data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_save_finetuning_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_finetuning_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_finetuning_data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/save_finetuning_data.ts":
/*!***********************************************!*\
  !*** ./src/pages/api/save_finetuning_data.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// API endpoint to save fine-tuning data after complete SpeedTraffic analysis\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol, phase1Data, phase2Data } = req.body;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    if (!phase1Data || !phase2Data) {\n        return res.status(400).json({\n            error: 'Both phase1Data and phase2Data are required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    try {\n        // Create finetuning directory if it doesn't exist\n        const finetuningDir = path__WEBPACK_IMPORTED_MODULE_1__.join(process.cwd(), 'src', 'data', 'finetuning');\n        if (!fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(finetuningDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0__.mkdirSync(finetuningDir, {\n                recursive: true\n            });\n        }\n        // Generate timestamp in YYYYMMDD_HHMMSS format\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const day = String(now.getDate()).padStart(2, '0');\n        const hours = String(now.getHours()).padStart(2, '0');\n        const minutes = String(now.getMinutes()).padStart(2, '0');\n        const seconds = String(now.getSeconds()).padStart(2, '0');\n        const timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;\n        const filename = `${ticker}_${timestamp}.json`;\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1__.join(finetuningDir, filename);\n        // Extract all service metrics for the 7 analysis components\n        // From Phase 1 data\n        const mfiResult = phase1Data.mfi;\n        const bollingerResult = phase1Data.bollinger;\n        const rsiResult = phase1Data.rsi;\n        const industryResult = phase1Data.industry;\n        const capmResult = phase1Data.capm;\n        const garchResult = phase1Data.garch;\n        // From Phase 2 data\n        const lstmResult = phase2Data.lstm;\n        // Helper functions for value extraction\n        const getMFIValue = (result)=>result?.mfi_14 || result?.mfi || 0;\n        const getBollingerValue = (result)=>result?.percent_b || result?.bollinger_position || result?.position || 0;\n        const getRSIValue = (result)=>result?.rsi_14 || result?.rsi || 0;\n        const getCAPMBeta = (result)=>result?.beta_market || result?.beta || 0;\n        const getCAPMR2 = (result)=>result?.r2_market || result?.r2 || 0;\n        const getCAPMTStat = (result)=>result?.tstat_market || result?.tstat || 0;\n        const getGARCHSigma = (result)=>result?.sigma_pct || result?.sigma || 0;\n        const getGARCHValue = (result)=>result?.var95_pct || result?.var_pct || result?.volatility_forecast || result?.volatility || 0;\n        const getIndustryName = (result)=>result?.industry || 'unknown';\n        const getIndustryBeta = (result)=>result?.beta || 0;\n        const getIndustryR2 = (result)=>result?.r2 || 0;\n        const getIndustryTStat = (result)=>result?.tstat || 0;\n        const getServiceTrafficLight = (result)=>result?.traffic_light || result?.color || 'red';\n        const calculateLSTMAccuracy = (predictions)=>{\n            if (!Array.isArray(predictions) || predictions.length === 0) return 0;\n            const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            return Math.round(correctCount / predictions.length * 100);\n        };\n        const getLSTMPredProbUp = (result)=>{\n            if (result?.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {\n                return result.predictions[0].pred_prob_up || 0;\n            }\n            return result?.pred_prob_up || 0;\n        };\n        const getLSTMTrafficLight = (result)=>{\n            if (result?.predictions && Array.isArray(result.predictions)) {\n                const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n                if (correctCount === 2) return 'green';\n                if (correctCount === 1) return 'yellow';\n                return 'red';\n            }\n            return result?.result_color || result?.color || 'red';\n        };\n        // Extract values for all 7 components\n        // 1. RSI\n        const rsiValue = rsiResult ? getRSIValue(rsiResult) : 0;\n        const rsiColor = rsiResult ? getServiceTrafficLight(rsiResult) : 'red';\n        // 2. Bollinger Bands\n        const bollingerValue = bollingerResult ? getBollingerValue(bollingerResult) : 0;\n        const bollingerColor = bollingerResult ? getServiceTrafficLight(bollingerResult) : 'red';\n        // 3. MFI\n        const mfiValue = mfiResult ? getMFIValue(mfiResult) : 0;\n        const mfiColor = mfiResult ? getServiceTrafficLight(mfiResult) : 'red';\n        // 4. CAPM Market Beta\n        const betaMarket = capmResult ? getCAPMBeta(capmResult) : 0;\n        const r2Market = capmResult ? getCAPMR2(capmResult) : 0;\n        const tstatMarket = capmResult ? getCAPMTStat(capmResult) : 0;\n        const capmColor = capmResult ? getServiceTrafficLight(capmResult) : 'red';\n        // 5. GARCH Volatility\n        const sigmaPct = garchResult ? getGARCHSigma(garchResult) : 0;\n        const var95Pct = garchResult ? getGARCHValue(garchResult) : 0;\n        const garchColor = garchResult ? getServiceTrafficLight(garchResult) : 'red';\n        // 6. Industry Regression\n        const industryName = industryResult ? getIndustryName(industryResult) : 'unknown';\n        const betaIndustry = industryResult ? getIndustryBeta(industryResult) : 0;\n        const r2Industry = industryResult ? getIndustryR2(industryResult) : 0;\n        const tstatIndustry = industryResult ? getIndustryTStat(industryResult) : 0;\n        const industryColor = industryResult ? getServiceTrafficLight(industryResult) : 'red';\n        // 7. LSTM\n        const lstmAccuracy = lstmResult ? calculateLSTMAccuracy(lstmResult.predictions || []) : 0;\n        const lstmPredProbUp = lstmResult ? getLSTMPredProbUp(lstmResult) : 0;\n        const lstmColor = lstmResult ? getLSTMTrafficLight(lstmResult) : 'red';\n        // Create fine-tuning data in OpenAI format with all 7 components\n        const finetuningData = {\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"\"\n                },\n                {\n                    role: \"user\",\n                    content: `[RSI value: ${rsiValue}, Traffic light: ${rsiColor}] [Bollinger %B: ${bollingerValue}, Traffic light: ${bollingerColor}] [MFI value: ${mfiValue}, Traffic light: ${mfiColor}] [Market Beta: ${betaMarket}, R²: ${r2Market}, t-stat: ${tstatMarket}, Traffic light: ${capmColor}] [Volatility: ${sigmaPct}%, VaR 95%: ${var95Pct}%, Traffic light: ${garchColor}] [Industry: ${industryName}, Beta: ${betaIndustry}, R²: ${r2Industry}, t-stat: ${tstatIndustry}, Traffic light: ${industryColor}] [LSTM accuracy: ${lstmAccuracy}%, Prediction probability up: ${lstmPredProbUp}, Traffic light: ${lstmColor}]`\n                },\n                {\n                    role: \"assistant\",\n                    content: \"\"\n                }\n            ]\n        };\n        fs__WEBPACK_IMPORTED_MODULE_0__.writeFileSync(filepath, JSON.stringify(finetuningData, null, 2), 'utf-8');\n        console.log(`[FINETUNING_DATA] Saved fine-tuning data to ${filename}`);\n        res.status(200).json({\n            success: true,\n            filename,\n            message: 'Fine-tuning data saved successfully'\n        });\n    } catch (error) {\n        console.error(`[FINETUNING_DATA] Failed to save fine-tuning data for ${ticker}:`, error);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Failed to save fine-tuning data',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/save_finetuning_data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_finetuning_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_finetuning_data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();